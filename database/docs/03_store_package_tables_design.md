# 门店付费设置表设计文档

## 概述

本文档描述了AI运营后台付费管理系统中门店付费设置相关的表结构设计，包括门店套餐关联表、门店个性化定价表和使用记录表，支持门店级别的定制化付费配置。

## 业务需求分析

根据需求文档，门店付费设置需要支持以下核心功能：

1. **门店套餐关联**：门店可以选择和绑定特定的付费套餐
2. **个性化定价**：门店可以设置独立于默认套餐的个性化价格
3. **状态管理**：支持启用→停用→删除的状态流转
4. **价格覆盖**：留空使用默认价格，填写使用个性化价格
5. **操作追踪**：记录门店套餐的变更历史

## 表结构设计

### 1. 门店套餐关联表 (hds_store_packages)

**表名**: `hds_store_packages`  
**用途**: 存储门店与套餐的关联关系，实现门店级别的套餐配置

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | int | 主键，自增 | 主键标识 |
| store_package_code | varchar(32) | 非空，唯一 | 门店套餐编码，系统生成 |
| hotel_code | varchar(64) | 非空 | 酒店编码，关联hds_hotel_info |
| package_id | int | 非空 | 套餐ID，关联hds_payment_packages |
| status | enum | 非空，默认DISABLED | 状态管理 |
| effective_date | datetime | 可空 | 生效时间 |
| expire_date | datetime | 可空 | 失效时间 |

#### 状态说明
- **ENABLED**: 启用状态，门店可以使用该套餐
- **DISABLED**: 停用状态，门店暂时不能使用该套餐
- **DELETED**: 删除状态，逻辑删除，前端隐藏

#### 业务规则
1. 门店套餐编码格式：SP_YYYYMMDD_XXXX
2. 一个门店可以关联多个套餐，但同一套餐只能关联一次
3. 状态流转：启用→停用→删除（不可逆）
4. 删除状态的记录仅前端隐藏，数据库保留

#### 索引设计
- **主键索引**: id
- **唯一索引**: store_package_code, (hotel_code, package_id)
- **普通索引**: hotel_code, package_id, status, effective_date

### 2. 门店个性化定价表 (hds_store_package_pricing)

**表名**: `hds_store_package_pricing`  
**用途**: 存储门店级别的个性化定价信息，支持覆盖默认套餐价格

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | int | 主键，自增 | 主键标识 |
| store_package_id | int | 非空 | 门店套餐ID |
| product_id | int | 非空 | 产品ID |
| period_type | enum | 非空 | 周期类型 |
| custom_market_price | decimal(10,2) | 可空 | 自定义门市价 |
| custom_discount_price | decimal(10,2) | 可空 | 自定义优惠价 |
| custom_discount_rate | decimal(5,2) | 可空 | 自定义折扣比例 |
| custom_fixed_price | decimal(10,2) | 可空 | 自定义一口价 |
| final_price | decimal(10,2) | 非空 | 最终价格 |
| is_custom_pricing | tinyint | 非空，默认0 | 是否使用自定义定价 |

#### 定价逻辑
1. **默认价格模式** (is_custom_pricing = 0):
   - 所有自定义价格字段为空
   - final_price 使用套餐默认价格

2. **自定义价格模式** (is_custom_pricing = 1):
   - 根据套餐的优惠方式选择对应的自定义字段
   - 一口价模式：使用 custom_fixed_price
   - 折扣模式：使用 custom_discount_rate

#### 价格计算规则
- **留空使用默认价格**：自定义字段为NULL时，使用套餐默认价格
- **填写使用个性化价格**：自定义字段有值时，使用门店个性化价格
- **不做价格高低校验**：设置多少展示多少，不限制价格范围

#### 索引设计
- **主键索引**: id
- **唯一索引**: (store_package_id, product_id, period_type)
- **普通索引**: store_package_id, product_id, period_type, final_price, is_custom_pricing

### 3. 门店套餐使用记录表 (hds_store_package_usage)

**表名**: `hds_store_package_usage`  
**用途**: 记录门店套餐的使用历史，支持套餐变更追踪

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | int | 主键，自增 | 主键标识 |
| hotel_code | varchar(64) | 非空 | 酒店编码 |
| store_package_id | int | 非空 | 门店套餐ID |
| usage_type | enum | 非空 | 使用类型 |
| usage_date | datetime | 非空 | 操作时间 |
| operator_id | varchar(64) | 非空 | 操作人ID |
| operator_name | varchar(64) | 非空 | 操作人名称 |
| operation_reason | varchar(255) | 可空 | 操作原因 |
| before_status | varchar(50) | 可空 | 操作前状态 |
| after_status | varchar(50) | 可空 | 操作后状态 |

#### 使用类型说明
- **ACTIVATE**: 激活套餐，门店开始使用
- **DEACTIVATE**: 停用套餐，门店暂停使用
- **MODIFY**: 修改套餐配置或价格
- **DELETE**: 删除套餐关联

## 业务流程设计

### 1. 门店套餐配置流程
1. **选择门店**：支持模糊输入和筛选
2. **选择套餐**：从可用套餐列表中选择
3. **同步信息**：自动同步门店和套餐基本信息
4. **个性化设置**：可选择是否设置个性化价格
5. **保存配置**：默认为停用状态，需手动启用

### 2. 个性化定价流程
1. **价格继承**：默认继承套餐价格
2. **自定义设置**：可独立设置月度/季度/年度价格
3. **价格计算**：系统自动计算最终价格
4. **生效机制**：保存后立即生效

### 3. 状态流转流程
1. **新建** → **停用**：新建门店套餐默认为停用状态
2. **停用** → **启用**：手动启用后门店可使用套餐
3. **启用** → **停用**：可随时停用套餐
4. **停用** → **删除**：只有停用状态才能删除
5. **删除**：逻辑删除，前端隐藏，数据库保留

## 与现有表的关联关系

### 关联表说明
1. **hds_hotel_info**: 通过hotel_code关联门店信息
2. **hds_payment_packages**: 通过package_id关联套餐信息
3. **hds_package_products**: 通过product_id关联产品信息
4. **hds_package_pricing**: 获取默认定价信息

### 数据一致性保证
1. **外键约束**: 确保关联数据的完整性
2. **唯一约束**: 防止重复关联和配置
3. **状态检查**: 确保状态流转的正确性
4. **审计追踪**: 记录所有变更操作

## 查询优化建议

### 常用查询场景
1. **门店套餐查询**: 根据hotel_code查询门店的套餐配置
2. **套餐使用统计**: 统计套餐在各门店的使用情况
3. **价格对比查询**: 对比默认价格和门店个性化价格
4. **操作历史查询**: 查询门店套餐的变更历史

### 索引优化
1. **复合索引**: (hotel_code, status) 支持门店套餐状态查询
2. **覆盖索引**: 包含常用查询字段，减少回表操作
3. **分区策略**: 按时间分区使用记录表，提升查询性能

## 注意事项

1. **价格精度**: 所有价格字段使用decimal(10,2)保证精度
2. **状态管理**: 严格按照状态流转规则操作
3. **数据备份**: 删除操作使用逻辑删除，保证数据可恢复
4. **权限控制**: 个性化定价需要相应的操作权限
5. **审计要求**: 所有操作都需要记录操作人和操作时间
