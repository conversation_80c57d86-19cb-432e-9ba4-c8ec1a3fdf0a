# 基础数据表设计文档

## 概述

本文档描述了AI运营后台付费管理系统的基础数据表结构设计。由于系统已存在门店表(`hds_hotel_info`)和员工表(`hds_employee`)，本设计基于现有表结构，补充付费管理所需的扩展表。

## 现有表结构说明

### 现有门店表 (hds_hotel_info)
- **主键**: `id`
- **门店标识**: `hotel_code` (唯一)
- **门店名称**: `hotel_name`
- **房间数量**: `total_room`
- **联系信息**: `main_person`, `phone`, `email`
- **状态字段**: `status` (1-正常, 0-停用, 2-未完成初始化)

### 现有员工表 (hds_employee)
- **主键**: `id`
- **用户标识**: `username` (唯一)
- **员工信息**: `name`, `mobile`, `email`
- **员工类型**: `type` (1-集团, 2-服务商, 3-酒店)
- **状态字段**: `status` (1-正常, 2-停用, 3-注销)

### 现有员工门店关联表 (hds_employee_hotel)
- **员工ID**: `employee_id`
- **门店编码**: `hotel_code`
- **角色编码**: `role_code`

## 新增扩展表设计

### 1. 付费管理配置表 (payment_configs)

**表名**: `payment_configs`
**用途**: 存储付费管理相关的系统配置信息

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| config_id | bigint(20) | 主键，自增 | 配置唯一标识 |
| config_key | varchar(100) | 非空，唯一 | 配置键 |
| config_value | text | 可空 | 配置值 |
| config_type | enum | 非空 | 配置类型 |
| config_group | varchar(50) | 默认PAYMENT | 配置分组 |
| description | varchar(255) | 可空 | 配置描述 |

#### 配置类型说明
- **STRING**: 字符串类型配置
- **NUMBER**: 数字类型配置
- **BOOLEAN**: 布尔类型配置
- **JSON**: JSON格式配置

### 2. 门店付费扩展信息表 (hotel_payment_info)

**表名**: `hotel_payment_info`
**用途**: 扩展现有门店表，存储付费管理相关的门店信息

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | bigint(20) | 主键，自增 | 主键标识 |
| hotel_code | varchar(64) | 非空，唯一 | 酒店编码，关联hds_hotel_info |
| payment_status | tinyint(1) | 非空，默认0 | 付费状态 |
| current_package_id | bigint(20) | 可空 | 当前使用的套餐ID |
| service_start_time | datetime | 可空 | 服务开始时间 |
| service_end_time | datetime | 可空 | 服务结束时间 |
| auto_renew | tinyint(1) | 非空，默认0 | 是否自动续费 |
| total_paid_amount | decimal(10,2) | 非空，默认0.00 | 累计支付金额 |

#### 付费状态说明
- **0**: 未付费
- **1**: 已付费
- **2**: 已过期

#### 与现有表的关联
- 通过`hotel_code`字段关联`hds_hotel_info.hotel_code`
- 获取门店基本信息（名称、房间数量、联系方式等）

### 3. 员工付费权限扩展表 (employee_payment_role)

**表名**: `employee_payment_role`
**用途**: 扩展现有员工表，存储付费管理相关的权限信息

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | bigint(20) | 主键，自增 | 主键标识 |
| employee_id | int(11) | 非空 | 员工ID，关联hds_employee |
| hotel_code | varchar(64) | 可空 | 酒店编码 |
| payment_role | enum | 非空 | 付费权限角色 |
| can_purchase | tinyint(1) | 非空，默认0 | 是否可以购买套餐 |
| can_view_bills | tinyint(1) | 非空，默认1 | 是否可以查看账单 |
| can_apply_invoice | tinyint(1) | 非空，默认0 | 是否可以申请发票 |

#### 付费权限角色说明
- **PAYMENT_ADMIN**: 付费管理员，拥有所有权限
- **PAYMENT_OPERATOR**: 付费操作员，可以购买和查看
- **PAYMENT_VIEWER**: 付费查看员，只能查看账单

#### 与现有表的关联
- 通过`employee_id`字段关联`hds_employee.id`
- 通过`hotel_code`字段关联`hds_hotel_info.hotel_code`
- 结合`hds_employee_hotel`表实现完整的权限控制

## 设计原则

### 1. 数据完整性

- 所有表都包含完整的审计字段（创建时间、更新时间、创建人、更新人）
- 使用软删除机制，保证数据的可追溯性
- 合理的字段约束和数据类型选择

### 2. 性能优化

- 为常用查询字段创建索引
- 使用合适的数据类型减少存储空间
- 避免过度索引影响写入性能

### 3. 扩展性

- 预留扩展字段支持业务发展
- 使用枚举类型便于状态管理
- 模块化设计便于后续扩展

### 4. 安全性

- 密码字段加密存储
- 用户权限分级管理
- 敏感信息访问控制

## 使用说明

1. 执行SQL脚本创建表结构
2. 根据业务需要插入初始化数据
3. 配置相应的应用程序连接参数
4. 建议定期备份重要数据

## 注意事项

1. 门店编码和用户名必须保持唯一性
2. 门店管理员必须关联具体的门店
3. 系统配置修改需要谨慎，建议备份原值
4. 删除操作建议使用软删除，避免数据丢失
