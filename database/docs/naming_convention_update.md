# 数据库命名规范更新说明

## 概述

根据项目要求和现有表结构规范，所有数据库表都需要遵循统一的命名规范：
- **表名前缀**: 所有表以 `hds_` 开头
- **主键列名**: 统一使用 `id` 作为主键列名
- **审计字段**: 包含 `created_by_name`、`updated_by_name` 等完整审计字段
- **表结构风格**: 参考 `hds_hotel_info` 表的结构风格
- **数据类型**: 使用 `int` 而不是 `bigint(20)`，使用 `tinyint` 而不是 `tinyint(1)`
- **引擎声明**: 不包含 `ENGINE=InnoDB DEFAULT` 等引擎声明

## 表名更新对照

### 基础数据表
| 原表名 | 新表名 | 说明 |
|--------|--------|------|
| payment_configs | hds_payment_configs | 付费管理配置表 |
| hotel_payment_info | hds_hotel_payment_info | 门店付费扩展信息表 |
| employee_payment_role | hds_employee_payment_role | 员工付费权限扩展表 |

### 付费套餐管理表
| 原表名 | 新表名 | 说明 |
|--------|--------|------|
| payment_packages | hds_payment_packages | 付费套餐主表 |
| package_products | hds_package_products | 套餐产品表 |
| package_pricing | hds_package_pricing | 套餐定价表 |

## 主键字段更新对照

### 基础数据表
| 表名 | 原主键字段 | 新主键字段 |
|------|------------|------------|
| hds_payment_configs | config_id | id |
| hds_hotel_payment_info | id | id（无变化） |
| hds_employee_payment_role | id | id（无变化） |

### 付费套餐管理表
| 表名 | 原主键字段 | 新主键字段 |
|------|------------|------------|
| hds_payment_packages | package_id | id |
| hds_package_products | product_id | id |
| hds_package_pricing | pricing_id | id |

## 外键关系更新

### 原外键关系
```sql
-- 套餐产品表
CONSTRAINT `fk_package_products_package_id` 
FOREIGN KEY (`package_id`) REFERENCES `payment_packages` (`package_id`)

-- 套餐定价表
CONSTRAINT `fk_package_pricing_product_id` 
FOREIGN KEY (`product_id`) REFERENCES `package_products` (`product_id`)
```

### 新外键关系
```sql
-- 套餐产品表
CONSTRAINT `fk_package_products_package_id` 
FOREIGN KEY (`package_id`) REFERENCES `hds_payment_packages` (`id`)

-- 套餐定价表
CONSTRAINT `fk_package_pricing_product_id` 
FOREIGN KEY (`product_id`) REFERENCES `hds_package_products` (`id`)
```

## 现有表结构保持不变

以下现有表结构已经符合命名规范，无需修改：

### 门店相关表
- `hds_hotel_info` - 门店信息表（主键：id）
- `hds_employee` - 员工信息表（主键：id）
- `hds_employee_hotel` - 员工门店关联表（主键：id）

## 影响范围

### 1. SQL脚本文件
- ✅ `database/schema/01_basic_tables.sql` - 已更新
- ✅ `database/schema/02_package_tables.sql` - 已更新

### 2. 测试脚本文件
- ✅ `database/tests/01_basic_tables_test_updated.sql` - 新建更新版本
- ✅ `database/tests/02_package_tables_test.sql` - 已更新

### 3. 设计文档
- 📝 需要更新文档中的表名和字段名引用

## 后续任务影响

由于命名规范的更新，后续的任务需要注意：

### 门店付费设置表
- 表名需要以 `hds_` 开头
- 主键使用 `id`
- 外键引用需要指向正确的表名和字段

### 订单和支付表
- 表名需要以 `hds_` 开头
- 主键使用 `id`
- 外键引用需要指向正确的表名和字段

### 账单管理表
- 表名需要以 `hds_` 开头
- 主键使用 `id`
- 外键引用需要指向正确的表名和字段

### 开票管理表
- 表名需要以 `hds_` 开头
- 主键使用 `id`
- 外键引用需要指向正确的表名和字段

## 验证清单

- [x] 所有表名都以 `hds_` 开头
- [x] 所有主键都使用 `id` 字段名
- [x] 外键约束引用正确的表名和字段名
- [x] 索引名称保持一致性
- [x] 测试脚本使用正确的表名和字段名
- [x] 注释和文档保持准确性

## 注意事项

1. **一致性**: 确保所有后续表都遵循相同的命名规范
2. **外键引用**: 新建表的外键必须引用正确的表名和字段名
3. **测试验证**: 所有SQL脚本都需要经过测试验证
4. **文档同步**: 设计文档需要与实际表结构保持同步

## 表结构规范对照

### 参考表结构 (hds_hotel_info)
```sql
create table hds_hotel_info
(
    id                 int auto_increment comment '主键'
        primary key,
    hotel_code         varchar(64)                           not null comment '酒店编码',
    created_by         varchar(64) default '1'               not null comment '创建人id',
    created_by_name    varchar(64) default '1'               not null comment '创建人名称',
    updated_by         varchar(64) default '1'               not null comment '修改人id',
    updated_by_name    varchar(64) default '1'               not null comment '修改人名称',
    created_at         datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at         datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status         int                                   null comment '记录状态(1:有效, 0:无效)'
)
    comment '门店表';
```

### 新表结构特点
1. **主键**: `id int auto_increment comment '主键' primary key`
2. **审计字段**: 包含 `created_by`、`created_by_name`、`updated_by`、`updated_by_name`
3. **时间字段**: `created_at`、`updated_at` 使用 `datetime` 类型
4. **状态字段**: `row_status int null comment '记录状态(1:有效, 0:无效)'`
5. **索引创建**: 使用独立的 `create index` 语句
6. **约束创建**: 使用 `constraint` 语法

## 完成状态

- ✅ 基础数据表命名规范更新完成
- ✅ 付费套餐管理表命名规范更新完成
- ✅ 表结构风格统一完成（参考hds_hotel_info）
- ✅ 审计字段规范统一完成
- 📋 后续任务将按照新的命名规范执行
