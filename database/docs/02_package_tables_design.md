# 付费套餐管理表设计文档

## 概述

本文档描述了AI运营后台付费管理系统中付费套餐管理相关的表结构设计，包括套餐主表、产品表和定价表，支持灵活的套餐配置和定价策略。

## 业务需求分析

根据需求文档，付费套餐管理需要支持以下核心功能：

1. **套餐配置**：自定义套餐名称（唯一，最大20字符）
2. **付费方式**：按房间数量收费 / 按门店收费
3. **优惠方式**：折扣 / 一口价
4. **产品组合**：每个套餐最多包含5个产品
5. **定价策略**：支持月度/季度/年度的灵活定价
6. **推荐机制**：支持设置推荐套餐（仅一个）

## 表结构设计

### 1. 付费套餐主表 (payment_packages)

**表名**: `payment_packages`  
**用途**: 存储付费套餐的基本信息和配置

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| package_id | bigint(20) | 主键，自增 | 套餐唯一标识 |
| package_code | varchar(32) | 非空，唯一 | 套餐编码，系统生成 |
| package_name | varchar(20) | 非空，唯一 | 套餐名称，最大20字符 |
| payment_mode | enum | 非空 | 付费方式 |
| discount_mode | enum | 非空 | 优惠方式 |
| status | enum | 非空，默认DISABLED | 套餐状态 |
| is_recommended | tinyint(1) | 非空，默认0 | 是否推荐套餐 |
| sort_order | int(11) | 非空，默认0 | 排序权重 |

#### 付费方式说明
- **ROOM_COUNT**: 按房间数量收费，最终费用 = 房间单价 × 房间数量
- **STORE_FIXED**: 按门店收费，每个酒店收取固定金额

#### 优惠方式说明
- **DISCOUNT**: 折扣模式，支持设置折扣比例
- **FIXED_PRICE**: 一口价模式，支持设置固定优惠价

#### 业务规则
1. 套餐名称必须唯一，不支持重复命名
2. 新增套餐默认为停用状态，需手动启用
3. 仅支持设置一个推荐套餐
4. 套餐编码格式：PKG_YYYYMMDD_XXXX

### 2. 套餐产品表 (package_products)

**表名**: `package_products`  
**用途**: 存储套餐包含的产品信息

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| product_id | bigint(20) | 主键，自增 | 产品唯一标识 |
| package_id | bigint(20) | 非空，外键 | 关联套餐ID |
| product_name | varchar(20) | 非空 | 产品名称，最大20字符 |
| monthly_market_price | decimal(10,2) | 非空 | 月度门市价 |
| monthly_discount_price | decimal(10,2) | 非空 | 月度优惠价 |
| product_descriptions | json | 可空 | 产品描述JSON数组 |
| sort_order | int(11) | 非空，默认0 | 产品排序 |

#### 产品描述格式
```json
[
  "产品功能一",
  "产品功能二",
  "产品功能三"
]
```
- 最多支持10条描述
- 每条描述最大50字符

#### 业务规则
1. 每个套餐最多包含5个产品
2. 必须设置月度基础价格（门市价和优惠价）
3. 立省金额 = 门市价 - 优惠价

### 3. 套餐定价表 (package_pricing)

**表名**: `package_pricing`  
**用途**: 存储套餐产品的详细定价信息

#### 字段设计

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| pricing_id | bigint(20) | 主键，自增 | 定价唯一标识 |
| product_id | bigint(20) | 非空，外键 | 关联产品ID |
| period_type | enum | 非空 | 周期类型 |
| market_price | decimal(10,2) | 非空 | 门市价 |
| discount_price | decimal(10,2) | 可空 | 优惠价（一口价模式） |
| discount_rate | decimal(5,2) | 可空 | 折扣比例（折扣模式） |
| final_price | decimal(10,2) | 非空 | 最终价格 |
| is_custom_price | tinyint(1) | 非空，默认0 | 是否自定义价格 |

#### 周期类型说明
- **MONTHLY**: 月度定价
- **QUARTERLY**: 季度定价  
- **YEARLY**: 年度定价

#### 价格计算规则

##### 一口价模式计算
- 月度优惠价：使用设置的月度优惠价
- 季度优惠价：
  - 如果设置了季度一口价 → 使用设置的一口价
  - 如果未设置 → 月度优惠价 × 3
- 年度优惠价：
  - 如果设置了年度一口价 → 使用设置的一口价
  - 如果未设置 → 月度优惠价 × 12

##### 折扣模式计算
- 月度优惠价：使用设置的月度优惠价
- 季度优惠价：月度优惠价 × 3 × (1 - 折扣比例)
- 年度优惠价：月度优惠价 × 12 × (1 - 折扣比例)

#### 折扣力度显示规则
- 月度折扣力度：显示为 "-"
- 季度折扣力度：显示设置的折扣比例，如 "立省10.0%"
- 年度折扣力度：显示设置的折扣比例，如 "立省20.0%"

## 索引设计

### 主要索引
1. **主键索引**：所有表的主键自动创建
2. **唯一索引**：
   - payment_packages.package_code
   - payment_packages.package_name
   - package_pricing(product_id, period_type)
3. **外键索引**：
   - package_products.package_id
   - package_pricing.product_id
4. **查询索引**：
   - payment_packages(payment_mode, status)
   - package_products.sort_order
   - package_pricing.period_type

## 数据完整性

### 外键约束
1. package_products.package_id → payment_packages.package_id
2. package_pricing.product_id → package_products.product_id

### 级联操作
- 删除套餐时，级联删除相关产品和定价信息
- 删除产品时，级联删除相关定价信息

### 检查约束
1. 价格字段必须 >= 0
2. 折扣比例范围：0-100%
3. 套餐名称长度：1-20字符
4. 产品名称长度：1-20字符

## 使用示例

### 创建套餐示例
```sql
-- 1. 创建套餐
INSERT INTO payment_packages (package_name, payment_mode, discount_mode) 
VALUES ('基础套餐', 'ROOM_COUNT', 'FIXED_PRICE');

-- 2. 添加产品
INSERT INTO package_products (package_id, product_name, monthly_market_price, monthly_discount_price) 
VALUES (1, 'AI客服', 1000.00, 800.00);

-- 3. 设置定价
INSERT INTO package_pricing (product_id, period_type, market_price, discount_price, final_price) 
VALUES 
(1, 'MONTHLY', 1000.00, 800.00, 800.00),
(1, 'QUARTERLY', 3000.00, 2040.00, 2040.00),
(1, 'YEARLY', 12000.00, 7200.00, 7200.00);
```

## 注意事项

1. 套餐编码由系统自动生成，不允许手动修改
2. 推荐套餐全局唯一，设置新推荐套餐时需取消原推荐
3. 价格计算需要考虑精度问题，建议保留2位小数
4. JSON字段存储产品描述，需要在应用层进行格式验证
5. 删除操作建议使用软删除，通过row_status字段控制
