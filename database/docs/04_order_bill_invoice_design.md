# 订单账单发票表设计文档

## 概述

本文档描述了AI运营后台付费管理系统中订单、账单、发票管理相关的表结构设计。基于对原始设计的分析，重新优化了业务逻辑和数据结构，解决了订单粒度、数据冗余、业务完整性等关键问题。

## 原始设计问题分析

### 1. 订单表设计问题

#### 问题1：订单粒度设计不合理
```sql
-- 原设计问题
create table hds_payment_order (
    package_id int not null comment '套餐ID',
    product_id int not null comment '产品ID',  -- 问题：订单直接关联到产品
    ...
)
```

**问题分析**：
- 订单应该购买整个套餐，而不是单个产品
- 当前设计导致一个套餐需要创建多个订单
- 不符合业务逻辑：用户购买的是套餐服务

#### 问题2：缺少关键业务字段
- 缺少服务开始/结束时间
- 缺少原价和优惠金额分离
- 缺少订单过期时间管理

### 2. 账单表数据冗余问题

```sql
-- 原设计冗余字段
package_name     varchar(64) not null,    -- 可以通过关联获取
product_name     varchar(64) not null,    -- 可以通过关联获取
order_created_at datetime not null,       -- 可以通过关联获取
```

**问题分析**：
- 违反数据库规范化原则
- 套餐/产品名称变更时，历史数据不一致
- 增加存储空间和维护复杂度

### 3. 发票表设计不完整

```sql
-- 原设计缺少重要字段
create table hds_invoice_detail (
    transaction_no varchar(64) not null,
    package_name   varchar(64) not null,
    bill_amount    decimal(10,2) not null
    -- 缺少：产品明细、购买周期、服务时间等
)
```

## 优化设计方案

### 1. 交易订单表 (hds_payment_order)

**设计目标**: 以套餐为粒度的订单管理，支持完整的订单生命周期

#### 核心优化
1. **订单粒度调整**: 从产品级别调整为套餐级别
2. **关联关系修正**: 关联门店套餐而不是直接关联产品
3. **业务字段完善**: 增加服务时间、价格明细等关键字段

#### 关键字段设计
| 字段名 | 数据类型 | 说明 | 优化点 |
|--------|----------|------|--------|
| hotel_package_id | int | 门店套餐ID | **修正**：关联门店套餐而不是产品 |
| purchase_period | enum | 购买周期 | **改进**：使用枚举替代数字编码 |
| original_amount | decimal(10,2) | 原价金额 | **新增**：支持价格明细分析 |
| discount_amount | decimal(10,2) | 优惠金额 | **新增**：支持优惠追踪 |
| service_start_time | datetime | 服务开始时间 | **新增**：支持服务周期管理 |
| service_end_time | datetime | 服务结束时间 | **新增**：支持服务周期管理 |
| order_status | enum | 订单状态 | **改进**：使用枚举提高可读性 |

#### 业务逻辑
```sql
-- 订单创建逻辑
1. 门店选择套餐和购买周期
2. 系统计算套餐总价（所有产品价格之和）
3. 应用优惠策略计算最终价格
4. 创建订单记录
5. 创建订单明细记录（记录具体产品信息）
```

### 2. 订单明细表 (hds_order_detail)

**设计目标**: 记录订单包含的具体产品信息，支持产品级别的追踪

#### 设计特点
1. **产品明细记录**: 记录订单包含的每个产品
2. **历史数据保护**: 冗余存储产品名称，保证历史数据准确性
3. **价格明细**: 记录每个产品的单价和小计

#### 业务价值
- 支持订单级别的产品明细查询
- 保证产品名称变更后历史数据的准确性
- 支持产品级别的销售分析

### 3. 账单表 (hds_payment_bill)

**设计目标**: 记录实际支付交易，减少数据冗余，提高数据一致性

#### 核心优化
1. **减少冗余**: 通过关联获取套餐/产品信息
2. **状态管理**: 增加退款和开票状态管理
3. **关联设计**: 通过order_id关联获取详细信息

#### 关键改进
| 原设计问题 | 优化方案 | 优势 |
|------------|----------|------|
| 冗余存储套餐名称 | 通过order_id关联获取 | 数据一致性 |
| 缺少退款管理 | 增加退款相关字段 | 完整业务支持 |
| 缺少开票状态 | 增加invoice_status字段 | 业务流程完整 |

#### 查询示例
```sql
-- 获取账单详细信息（无冗余设计）
SELECT 
    b.bill_no,
    b.payment_amount,
    o.order_no,
    pkg.package_name,
    h.hotel_name
FROM hds_payment_bill b
JOIN hds_payment_order o ON b.order_id = o.id
JOIN hds_hotel_package hp ON o.hotel_package_id = hp.id
JOIN hds_payment_package pkg ON hp.package_id = pkg.id
JOIN hds_hotel_info h ON o.hotel_code = h.hotel_code;
```

### 4. 发票管理表组

**设计目标**: 支持完整的发票申请、审核、开票流程

#### 表结构组成
1. **hds_invoice_info**: 门店发票信息预设
2. **hds_invoice_application**: 发票申请主表
3. **hds_invoice_detail**: 发票明细表

#### 业务流程设计
```
1. 门店预设发票信息 (hds_invoice_info)
   ↓
2. 选择账单申请开票 (hds_invoice_application)
   ↓
3. 系统生成发票明细 (hds_invoice_detail)
   ↓
4. 审核流程 (PENDING → REVIEWING → INVOICED/REJECTED)
   ↓
5. 开票完成，更新账单开票状态
```

#### 发票明细优化
```sql
-- 优化后的发票明细设计
create table hds_invoice_detail (
    application_id    int not null,
    bill_id          int not null,        -- 关联账单
    order_id         int not null,        -- 关联订单
    product_name     varchar(64) not null, -- 产品名称
    service_period   varchar(32) not null, -- 服务周期
    service_start_time datetime not null,  -- 服务开始时间
    service_end_time   datetime not null,  -- 服务结束时间
    bill_amount      decimal(10,2) not null -- 账单金额
);
```

## 数据流转设计

### 1. 订单到账单流转
```sql
-- 订单支付成功后创建账单
INSERT INTO hds_payment_bill (
    bill_no, transaction_no, order_id, hotel_code, 
    payment_amount, payment_method, transaction_at
)
SELECT 
    CONCAT('BILL_', DATE_FORMAT(NOW(), '%Y%m%d'), '_', LPAD(NEXT_ID, 6, '0')),
    @transaction_no,
    o.id,
    o.hotel_code,
    o.order_amount,
    @payment_method,
    NOW()
FROM hds_payment_order o
WHERE o.id = @order_id;

-- 更新订单状态
UPDATE hds_payment_order 
SET order_status = 'PAID', paid_at = NOW()
WHERE id = @order_id;
```

### 2. 账单到发票流转
```sql
-- 创建发票申请
INSERT INTO hds_invoice_application (
    application_no, hotel_code, invoice_title, tax_number,
    invoice_amount, receive_email, bill_count
)
SELECT 
    CONCAT('INV_', DATE_FORMAT(NOW(), '%Y%m%d'), '_', LPAD(NEXT_ID, 6, '0')),
    @hotel_code,
    ii.invoice_title,
    ii.tax_number,
    SUM(b.payment_amount),
    ii.receive_email,
    COUNT(b.id)
FROM hds_payment_bill b
JOIN hds_invoice_info ii ON b.hotel_code = ii.hotel_code
WHERE b.id IN (@bill_ids) AND b.invoice_status = 'NONE';

-- 创建发票明细
INSERT INTO hds_invoice_detail (
    application_id, bill_id, order_id, product_name,
    service_period, service_start_time, service_end_time, bill_amount
)
SELECT 
    @application_id,
    b.id,
    o.id,
    GROUP_CONCAT(od.product_name),
    o.purchase_period,
    o.service_start_time,
    o.service_end_time,
    b.payment_amount
FROM hds_payment_bill b
JOIN hds_payment_order o ON b.order_id = o.id
JOIN hds_order_detail od ON o.id = od.order_id
WHERE b.id IN (@bill_ids)
GROUP BY b.id;

-- 更新账单开票状态
UPDATE hds_payment_bill 
SET invoice_status = 'APPLIED'
WHERE id IN (@bill_ids);
```

## 性能优化设计

### 1. 索引策略
```sql
-- 订单表关键索引
CREATE INDEX idx_hotel_code ON hds_payment_order (hotel_code);
CREATE INDEX idx_order_status ON hds_payment_order (order_status);
CREATE INDEX idx_service_time ON hds_payment_order (service_start_time, service_end_time);

-- 账单表关键索引
CREATE INDEX idx_order_id ON hds_payment_bill (order_id);
CREATE INDEX idx_transaction_at ON hds_payment_bill (transaction_at);
CREATE INDEX idx_invoice_status ON hds_payment_bill (invoice_status);

-- 发票表关键索引
CREATE INDEX idx_application_status ON hds_invoice_application (application_status);
CREATE INDEX idx_invoice_time ON hds_invoice_application (invoice_time);
```

### 2. 查询优化
```sql
-- 门店订单统计查询优化
SELECT 
    o.hotel_code,
    COUNT(*) as order_count,
    SUM(o.order_amount) as total_amount,
    COUNT(CASE WHEN o.order_status = 'PAID' THEN 1 END) as paid_count
FROM hds_payment_order o
WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY o.hotel_code;

-- 账单开票状态统计
SELECT 
    b.invoice_status,
    COUNT(*) as bill_count,
    SUM(b.payment_amount) as total_amount
FROM hds_payment_bill b
WHERE b.transaction_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY b.invoice_status;
```

## 数据完整性保证

### 1. 外键约束
```sql
-- 订单明细外键
ALTER TABLE hds_order_detail 
ADD CONSTRAINT fk_order_detail_order 
FOREIGN KEY (order_id) REFERENCES hds_payment_order(id);

-- 账单外键
ALTER TABLE hds_payment_bill 
ADD CONSTRAINT fk_bill_order 
FOREIGN KEY (order_id) REFERENCES hds_payment_order(id);

-- 发票明细外键
ALTER TABLE hds_invoice_detail 
ADD CONSTRAINT fk_invoice_detail_application 
FOREIGN KEY (application_id) REFERENCES hds_invoice_application(id);
```

### 2. 业务约束
```sql
-- 订单金额约束
ALTER TABLE hds_payment_order 
ADD CONSTRAINT chk_order_amount 
CHECK (order_amount = original_amount - discount_amount);

-- 账单金额约束
ALTER TABLE hds_payment_bill 
ADD CONSTRAINT chk_payment_amount 
CHECK (payment_amount > 0);

-- 发票金额约束
ALTER TABLE hds_invoice_application 
ADD CONSTRAINT chk_invoice_amount 
CHECK (invoice_amount > 0);
```

## 总结

优化后的设计具有以下优势：

1. **业务逻辑正确**: 订单以套餐为粒度，符合实际业务需求
2. **数据一致性**: 减少冗余，通过关联保证数据一致性
3. **扩展性强**: 支持复杂的业务流程和状态管理
4. **性能优化**: 合理的索引设计和查询优化
5. **完整性保证**: 外键约束和业务约束确保数据完整性

这个设计方案能够更好地支持订单、账单、发票的完整业务流程管理。
