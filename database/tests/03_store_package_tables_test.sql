-- =====================================================
-- 门店付费设置表测试脚本
-- 用途: 验证门店付费设置表结构的正确性和业务逻辑
-- =====================================================

-- 测试表结构创建
SOURCE database/schema/03_store_package_tables.sql;

-- =====================================================
-- 1. 测试门店套餐关联表 (hds_store_packages)
-- =====================================================

-- 插入测试数据（假设已有门店和套餐数据）
INSERT INTO hds_store_packages (store_package_code, hotel_code, package_id, status, effective_date, expire_date, remark, created_by, created_by_name) 
VALUES 
('SP_20250110_0001', 'HOTEL_A_001', 1, 'ENABLED', NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), '基础套餐配置', '1', '系统管理员'),
('SP_20250110_0002', 'HOTEL_B_002', 2, 'DISABLED', NOW(), DATE_ADD(NOW(), INTERVAL 1 YEAR), '标准套餐配置', '1', '系统管理员'),
('SP_20250110_0003', 'HOTEL_C_003', 1, 'ENABLED', NOW(), DATE_ADD(NOW(), INTERVAL 6 MONTH), '基础套餐配置', '1', '系统管理员');

-- 验证数据插入
SELECT COUNT(*) as store_package_count FROM hds_store_packages WHERE row_status = 1;

-- 测试唯一约束
-- 以下语句应该失败（门店套餐编码重复）
-- INSERT INTO hds_store_packages (store_package_code, hotel_code, package_id) VALUES ('SP_20250110_0001', 'HOTEL_D_004', 1);

-- 测试门店套餐唯一约束
-- 以下语句应该失败（同一门店不能重复关联同一套餐）
-- INSERT INTO hds_store_packages (store_package_code, hotel_code, package_id) VALUES ('SP_20250110_0004', 'HOTEL_A_001', 1);

-- 测试状态查询
SELECT 
    store_package_code,
    hotel_code,
    package_id,
    status,
    effective_date,
    expire_date
FROM hds_store_packages 
WHERE status = 'ENABLED';

-- =====================================================
-- 2. 测试门店个性化定价表 (hds_store_package_pricing)
-- =====================================================

-- 插入默认定价数据（使用套餐默认价格）
INSERT INTO hds_store_package_pricing (store_package_id, product_id, period_type, final_price, is_custom_pricing, created_by, created_by_name) 
VALUES 
-- 门店A的基础套餐产品（使用默认价格）
(1, 1, 'MONTHLY', 400.00, 0, '1', '系统管理员'),
(1, 1, 'QUARTERLY', 1000.00, 0, '1', '系统管理员'),
(1, 1, 'YEARLY', 3600.00, 0, '1', '系统管理员'),
(1, 2, 'MONTHLY', 600.00, 0, '1', '系统管理员'),
(1, 2, 'QUARTERLY', 1800.00, 0, '1', '系统管理员'),
(1, 2, 'YEARLY', 7200.00, 0, '1', '系统管理员');

-- 插入自定义定价数据（门店个性化价格）
INSERT INTO hds_store_package_pricing (store_package_id, product_id, period_type, custom_market_price, custom_discount_price, final_price, is_custom_pricing, price_calculation_note, created_by, created_by_name) 
VALUES 
-- 门店C的基础套餐产品（自定义一口价）
(3, 1, 'MONTHLY', 500.00, 350.00, 350.00, 1, '门店专属优惠价', '1', '系统管理员'),
(3, 1, 'QUARTERLY', 1500.00, 900.00, 900.00, 1, '季度专属一口价', '1', '系统管理员'),
(3, 1, 'YEARLY', 6000.00, 3000.00, 3000.00, 1, '年度专属一口价', '1', '系统管理员');

-- 验证定价数据插入
SELECT COUNT(*) as pricing_count FROM hds_store_package_pricing WHERE row_status = 1;

-- 测试定价查询（默认价格 vs 自定义价格）
SELECT 
    sp.hotel_code,
    sp.store_package_code,
    spp.product_id,
    spp.period_type,
    spp.custom_market_price,
    spp.custom_discount_price,
    spp.final_price,
    spp.is_custom_pricing,
    CASE 
        WHEN spp.is_custom_pricing = 1 THEN '自定义价格'
        ELSE '默认价格'
    END as pricing_type
FROM hds_store_packages sp
JOIN hds_store_package_pricing spp ON sp.id = spp.store_package_id
WHERE sp.row_status = 1 AND spp.row_status = 1
ORDER BY sp.hotel_code, spp.product_id, 
    CASE spp.period_type 
        WHEN 'MONTHLY' THEN 1 
        WHEN 'QUARTERLY' THEN 2 
        WHEN 'YEARLY' THEN 3 
    END;

-- =====================================================
-- 3. 测试门店套餐使用记录表 (hds_store_package_usage)
-- =====================================================

-- 插入使用记录数据
INSERT INTO hds_store_package_usage (hotel_code, store_package_id, usage_type, usage_date, operator_id, operator_name, operation_reason, before_status, after_status, created_by, created_by_name) 
VALUES 
('HOTEL_A_001', 1, 'ACTIVATE', NOW(), '1', '系统管理员', '门店申请开通基础套餐', 'DISABLED', 'ENABLED', '1', '系统管理员'),
('HOTEL_B_002', 2, 'ACTIVATE', DATE_SUB(NOW(), INTERVAL 1 DAY), '1', '系统管理员', '门店申请开通标准套餐', 'DISABLED', 'ENABLED', '1', '系统管理员'),
('HOTEL_B_002', 2, 'DEACTIVATE', NOW(), '1', '系统管理员', '门店暂停使用套餐', 'ENABLED', 'DISABLED', '1', '系统管理员'),
('HOTEL_C_003', 3, 'ACTIVATE', NOW(), '1', '系统管理员', '门店开通基础套餐并设置个性化价格', 'DISABLED', 'ENABLED', '1', '系统管理员');

-- 验证使用记录数据插入
SELECT COUNT(*) as usage_count FROM hds_store_package_usage WHERE row_status = 1;

-- 测试使用记录查询
SELECT 
    hotel_code,
    usage_type,
    usage_date,
    operator_name,
    operation_reason,
    before_status,
    after_status
FROM hds_store_package_usage 
WHERE row_status = 1
ORDER BY usage_date DESC;

-- =====================================================
-- 4. 综合业务逻辑测试
-- =====================================================

-- 测试门店套餐完整信息查询
SELECT 
    h.hotel_name,
    h.hotel_code,
    h.total_room,
    sp.store_package_code,
    sp.status as package_status,
    sp.effective_date,
    sp.expire_date,
    pkg.package_name,
    pkg.payment_mode,
    pkg.discount_mode
FROM hds_hotel_info h
JOIN hds_store_packages sp ON h.hotel_code = sp.hotel_code
JOIN hds_payment_packages pkg ON sp.package_id = pkg.id
WHERE h.row_status = 1 AND sp.row_status = 1 AND pkg.row_status = 1;

-- 测试门店个性化定价对比
SELECT 
    h.hotel_name,
    prd.product_name,
    spp.period_type,
    pp.final_price as default_price,
    spp.final_price as store_price,
    (spp.final_price - pp.final_price) as price_difference,
    CASE 
        WHEN spp.is_custom_pricing = 1 THEN '个性化定价'
        ELSE '默认定价'
    END as pricing_mode
FROM hds_hotel_info h
JOIN hds_store_packages sp ON h.hotel_code = sp.hotel_code
JOIN hds_store_package_pricing spp ON sp.id = spp.store_package_id
JOIN hds_package_products prd ON spp.product_id = prd.id
JOIN hds_package_pricing pp ON prd.id = pp.product_id AND spp.period_type = pp.period_type
WHERE h.row_status = 1 AND sp.row_status = 1 AND spp.row_status = 1 
    AND prd.row_status = 1 AND pp.row_status = 1
ORDER BY h.hotel_name, prd.product_name, spp.period_type;

-- =====================================================
-- 5. 状态流转测试
-- =====================================================

-- 测试状态更新
UPDATE hds_store_packages SET status = 'DISABLED' WHERE id = 1;
SELECT hotel_code, status FROM hds_store_packages WHERE id = 1;

-- 测试删除状态（逻辑删除）
UPDATE hds_store_packages SET status = 'DELETED' WHERE id = 2;
SELECT COUNT(*) as active_packages FROM hds_store_packages WHERE status != 'DELETED';
SELECT COUNT(*) as deleted_packages FROM hds_store_packages WHERE status = 'DELETED';

-- =====================================================
-- 6. 性能测试
-- =====================================================

-- 测试索引性能
EXPLAIN SELECT * FROM hds_store_packages WHERE hotel_code = 'HOTEL_A_001';
EXPLAIN SELECT * FROM hds_store_packages WHERE package_id = 1;
EXPLAIN SELECT * FROM hds_store_package_pricing WHERE store_package_id = 1;
EXPLAIN SELECT * FROM hds_store_package_usage WHERE hotel_code = 'HOTEL_A_001';

-- =====================================================
-- 测试完成，清理测试数据
-- =====================================================

-- 注意：在生产环境中不要执行以下清理语句
-- DELETE FROM hds_store_package_usage;
-- DELETE FROM hds_store_package_pricing;
-- DELETE FROM hds_store_packages;

SELECT '门店付费设置表测试完成' as test_result;
