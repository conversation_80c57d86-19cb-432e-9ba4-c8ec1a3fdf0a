-- =====================================================
-- 基础数据表测试脚本（更新版）
-- 用途: 验证基础表结构的正确性和完整性
-- 注意: 基于现有的hds_hotel_info和hds_employee表进行测试，所有表以hds_开头，主键为id
-- =====================================================

-- 测试表结构创建
SOURCE database/schema/01_basic_tables.sql;

-- =====================================================
-- 1. 测试现有门店表数据查询 (hds_hotel_info)
-- =====================================================

-- 查询现有门店数据
SELECT 
    hotel_code,
    hotel_name,
    total_room,
    main_person,
    phone,
    status
FROM hds_hotel_info 
WHERE row_status = 1 
LIMIT 5;

-- =====================================================
-- 2. 测试门店付费扩展信息表 (hds_hotel_payment_info)
-- =====================================================

-- 为现有门店插入付费扩展信息
INSERT INTO hds_hotel_payment_info (hotel_code, payment_status, service_start_time, service_end_time, created_by) 
SELECT 
    hotel_code,
    0 as payment_status,
    NOW() as service_start_time,
    DATE_ADD(NOW(), INTERVAL 30 DAY) as service_end_time,
    '1' as created_by
FROM hds_hotel_info 
WHERE row_status = 1 
LIMIT 3;

-- 验证数据插入
SELECT COUNT(*) as payment_info_count FROM hds_hotel_payment_info WHERE row_status = 1;

-- 测试门店付费信息关联查询
SELECT 
    h.hotel_name,
    h.hotel_code,
    h.total_room,
    p.payment_status,
    p.service_start_time,
    p.service_end_time
FROM hds_hotel_info h
LEFT JOIN hds_hotel_payment_info p ON h.hotel_code = p.hotel_code
WHERE h.row_status = 1 AND p.row_status = 1;

-- =====================================================
-- 3. 测试现有员工表数据查询 (hds_employee)
-- =====================================================

-- 查询现有员工数据
SELECT 
    username,
    name,
    mobile,
    type,
    status
FROM hds_employee 
WHERE row_status = 1 
LIMIT 5;

-- =====================================================
-- 4. 测试员工付费权限扩展表 (hds_employee_payment_role)
-- =====================================================

-- 为现有员工插入付费权限信息
INSERT INTO hds_employee_payment_role (employee_id, hotel_code, payment_role, can_purchase, can_view_bills, can_apply_invoice, created_by) 
SELECT 
    e.id as employee_id,
    eh.hotel_code,
    CASE 
        WHEN e.type = 1 THEN 'PAYMENT_ADMIN'
        WHEN e.type = 2 THEN 'PAYMENT_OPERATOR'
        ELSE 'PAYMENT_VIEWER'
    END as payment_role,
    CASE WHEN e.type IN (1,2) THEN 1 ELSE 0 END as can_purchase,
    1 as can_view_bills,
    CASE WHEN e.type = 1 THEN 1 ELSE 0 END as can_apply_invoice,
    '1' as created_by
FROM hds_employee e
JOIN hds_employee_hotel eh ON e.id = eh.employee_id
WHERE e.row_status = 1 AND eh.row_status = 1
LIMIT 5;

-- 验证数据插入
SELECT COUNT(*) as payment_role_count FROM hds_employee_payment_role WHERE row_status = 1;

-- 测试员工付费权限关联查询
SELECT 
    e.username,
    e.name,
    h.hotel_name,
    pr.payment_role,
    pr.can_purchase,
    pr.can_view_bills,
    pr.can_apply_invoice
FROM hds_employee e
JOIN hds_employee_payment_role pr ON e.id = pr.employee_id
LEFT JOIN hds_hotel_info h ON pr.hotel_code = h.hotel_code
WHERE e.row_status = 1 AND pr.row_status = 1;

-- =====================================================
-- 5. 测试付费管理配置表 (hds_payment_configs)
-- =====================================================

-- 插入测试配置
INSERT INTO hds_payment_configs (config_key, config_value, config_type, config_group, description, created_by) 
VALUES 
('system.name', 'AI运营后台付费管理系统', 'STRING', 'SYSTEM', '系统名称', '1'),
('payment.timeout', '1800', 'NUMBER', 'PAYMENT', '支付超时时间（秒）', '1'),
('email.enabled', 'true', 'BOOLEAN', 'EMAIL', '是否启用邮件通知', '1'),
('wechat.config', '{"appid":"wx123456","secret":"secret123"}', 'JSON', 'PAYMENT', '微信支付配置', '1'),
('alipay.config', '{"app_id":"2021001234567890","private_key":"MII..."}', 'JSON', 'PAYMENT', '支付宝支付配置', '1');

-- 验证配置数据
SELECT config_group, COUNT(*) as config_count FROM hds_payment_configs GROUP BY config_group;

-- 测试配置查询
SELECT config_key, config_value, config_type FROM hds_payment_configs WHERE config_group = 'PAYMENT';

-- =====================================================
-- 6. 数据完整性测试
-- =====================================================

-- 测试审计字段
SELECT 
    'hds_hotel_payment_info' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM hds_hotel_payment_info
UNION ALL
SELECT 
    'hds_employee_payment_role' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM hds_employee_payment_role
UNION ALL
SELECT 
    'hds_payment_configs' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM hds_payment_configs;

-- 测试软删除
UPDATE hds_hotel_payment_info SET row_status = 0 WHERE id = 1;
SELECT COUNT(*) as active_records FROM hds_hotel_payment_info WHERE row_status = 1;
SELECT COUNT(*) as deleted_records FROM hds_hotel_payment_info WHERE row_status = 0;

-- =====================================================
-- 7. 索引性能测试
-- =====================================================

-- 测试门店付费信息查询性能
EXPLAIN SELECT * FROM hds_hotel_payment_info WHERE hotel_code = 'HOTEL_001';
EXPLAIN SELECT * FROM hds_hotel_payment_info WHERE payment_status = 1;

-- 测试员工付费权限查询性能
EXPLAIN SELECT * FROM hds_employee_payment_role WHERE employee_id = 1;
EXPLAIN SELECT * FROM hds_employee_payment_role WHERE payment_role = 'PAYMENT_ADMIN';

-- 测试配置查询性能
EXPLAIN SELECT * FROM hds_payment_configs WHERE config_key = 'system.name';

-- =====================================================
-- 测试完成，清理测试数据
-- =====================================================

-- 注意：在生产环境中不要执行以下清理语句
-- DELETE FROM hds_hotel_payment_info;
-- DELETE FROM hds_employee_payment_role;
-- DELETE FROM hds_payment_configs;

SELECT '基础数据表测试完成（hds_前缀版本）' as test_result;
