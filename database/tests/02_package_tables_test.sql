-- =====================================================
-- 付费套餐管理表测试脚本
-- 用途: 验证套餐管理表结构的正确性和业务逻辑
-- =====================================================

-- 测试表结构创建
SOURCE database/schema/02_package_tables.sql;

-- =====================================================
-- 1. 测试付费套餐主表 (hds_payment_packages)
-- =====================================================

-- 插入测试套餐数据
INSERT INTO hds_payment_packages (package_name, payment_mode, discount_mode, status, is_recommended, sort_order, description, created_by)
VALUES
('基础套餐', 'ROOM_COUNT', 'FIXED_PRICE', 'ENABLED', 1, 100, '适合小型酒店的基础AI服务套餐', '1'),
('标准套餐', 'ROOM_COUNT', 'DISCOUNT', 'ENABLED', 0, 200, '适合中型酒店的标准AI服务套餐', '1'),
('高级套餐', 'STORE_FIXED', 'FIXED_PRICE', 'ENABLED', 0, 300, '适合大型酒店的高级AI服务套餐', '1');

-- 验证套餐数据插入
SELECT COUNT(*) as package_count FROM hds_payment_packages WHERE row_status = 1;

-- 测试套餐名称唯一约束
-- 以下语句应该失败（套餐名称重复）
-- INSERT INTO hds_payment_packages (package_name, payment_mode, discount_mode) VALUES ('基础套餐', 'ROOM_COUNT', 'FIXED_PRICE');

-- 测试推荐套餐查询
SELECT package_name, is_recommended FROM hds_payment_packages WHERE is_recommended = 1;

-- =====================================================
-- 2. 测试套餐产品表 (hds_package_products)
-- =====================================================

-- 为每个套餐添加产品
INSERT INTO hds_package_products (package_id, product_name, monthly_market_price, monthly_discount_price, product_descriptions, sort_order, created_by)
VALUES
-- 基础套餐产品
(1, 'AI客服基础版', 500.00, 400.00, '["智能问答", "24小时在线", "基础数据分析"]', 1, '1'),
(1, 'OTA智能体基础版', 800.00, 600.00, '["订单管理", "客户服务", "基础报表"]', 2, '1'),

-- 标准套餐产品
(2, 'AI客服标准版', 800.00, 640.00, '["智能问答", "24小时在线", "高级数据分析", "多语言支持"]', 1, '1'),
(2, 'OTA智能体标准版', 1200.00, 960.00, '["订单管理", "客户服务", "高级报表", "营销工具"]', 2, '1'),
(2, '数据分析工具', 600.00, 480.00, '["实时数据", "趋势分析", "自定义报表"]', 3, '1'),

-- 高级套餐产品
(3, 'AI客服高级版', 1200.00, 900.00, '["智能问答", "24小时在线", "高级数据分析", "多语言支持", "定制化服务"]', 1, '1'),
(3, 'OTA智能体高级版', 1800.00, 1350.00, '["订单管理", "客户服务", "高级报表", "营销工具", "API接口"]', 2, '1');

-- 验证产品数据插入
SELECT COUNT(*) as product_count FROM hds_package_products WHERE row_status = 1;

-- 测试套餐产品关联查询
SELECT 
    p.package_name,
    pr.product_name,
    pr.monthly_market_price,
    pr.monthly_discount_price,
    (pr.monthly_market_price - pr.monthly_discount_price) as monthly_save_amount
FROM payment_packages p
JOIN package_products pr ON p.package_id = pr.package_id
WHERE p.row_status = 1 AND pr.row_status = 1
ORDER BY p.sort_order, pr.sort_order;

-- =====================================================
-- 3. 测试套餐定价表 (package_pricing)
-- =====================================================

-- 为每个产品添加定价信息
-- 基础套餐 - AI客服基础版 (一口价模式)
INSERT INTO package_pricing (product_id, period_type, market_price, discount_price, final_price, is_custom_price, created_by) 
VALUES 
(1, 'MONTHLY', 500.00, 400.00, 400.00, 0, '1'),
(1, 'QUARTERLY', 1500.00, 1000.00, 1000.00, 1, '1'),  -- 自定义季度一口价
(1, 'YEARLY', 6000.00, 3600.00, 3600.00, 1, '1');     -- 自定义年度一口价

-- 基础套餐 - OTA智能体基础版 (一口价模式)
INSERT INTO package_pricing (product_id, period_type, market_price, discount_price, final_price, is_custom_price, created_by) 
VALUES 
(2, 'MONTHLY', 800.00, 600.00, 600.00, 0, '1'),
(2, 'QUARTERLY', 2400.00, 1800.00, 1800.00, 0, '1'),  -- 默认计算：600*3
(2, 'YEARLY', 9600.00, 7200.00, 7200.00, 0, '1');     -- 默认计算：600*12

-- 标准套餐 - AI客服标准版 (折扣模式)
INSERT INTO package_pricing (product_id, period_type, market_price, discount_price, discount_rate, final_price, is_custom_price, created_by) 
VALUES 
(3, 'MONTHLY', 800.00, 640.00, NULL, 640.00, 0, '1'),
(3, 'QUARTERLY', 2400.00, NULL, 15.00, 1632.00, 0, '1'),  -- 640*3*(1-0.15)=1632
(3, 'YEARLY', 9600.00, NULL, 25.00, 5760.00, 0, '1');     -- 640*12*(1-0.25)=5760

-- 验证定价数据插入
SELECT COUNT(*) as pricing_count FROM package_pricing WHERE row_status = 1;

-- 测试完整的套餐定价查询
SELECT 
    pkg.package_name,
    pkg.payment_mode,
    pkg.discount_mode,
    prd.product_name,
    prc.period_type,
    prc.market_price,
    prc.discount_price,
    prc.discount_rate,
    prc.final_price,
    CASE 
        WHEN prc.period_type = 'MONTHLY' THEN '-'
        WHEN prc.discount_rate IS NOT NULL THEN CONCAT('立省', prc.discount_rate, '%')
        ELSE CONCAT('立省¥', (prc.market_price - prc.final_price))
    END as save_info
FROM payment_packages pkg
JOIN package_products prd ON pkg.package_id = prd.package_id
JOIN package_pricing prc ON prd.product_id = prc.product_id
WHERE pkg.row_status = 1 AND prd.row_status = 1 AND prc.row_status = 1
ORDER BY pkg.sort_order, prd.sort_order, 
    CASE prc.period_type 
        WHEN 'MONTHLY' THEN 1 
        WHEN 'QUARTERLY' THEN 2 
        WHEN 'YEARLY' THEN 3 
    END;

-- =====================================================
-- 4. 业务逻辑测试
-- =====================================================

-- 测试推荐套餐唯一性
UPDATE payment_packages SET is_recommended = 1 WHERE package_id = 2;
SELECT package_name, is_recommended FROM payment_packages WHERE is_recommended = 1;

-- 测试套餐状态管理
UPDATE payment_packages SET status = 'DISABLED' WHERE package_id = 3;
SELECT package_name, status FROM payment_packages WHERE package_id = 3;

-- 测试产品描述JSON格式
SELECT 
    product_name,
    JSON_EXTRACT(product_descriptions, '$[0]') as first_feature,
    JSON_LENGTH(product_descriptions) as feature_count
FROM package_products 
WHERE product_descriptions IS NOT NULL;

-- =====================================================
-- 5. 性能测试
-- =====================================================

-- 测试索引性能
EXPLAIN SELECT * FROM payment_packages WHERE package_name = '基础套餐';
EXPLAIN SELECT * FROM payment_packages WHERE status = 'ENABLED' AND payment_mode = 'ROOM_COUNT';
EXPLAIN SELECT * FROM package_products WHERE package_id = 1;
EXPLAIN SELECT * FROM package_pricing WHERE product_id = 1 AND period_type = 'MONTHLY';

-- =====================================================
-- 6. 数据完整性测试
-- =====================================================

-- 测试外键约束
-- 以下语句应该失败（引用不存在的套餐ID）
-- INSERT INTO package_products (package_id, product_name, monthly_market_price, monthly_discount_price) 
-- VALUES (999, '测试产品', 100.00, 80.00);

-- 测试级联删除
-- DELETE FROM payment_packages WHERE package_id = 3;
-- SELECT COUNT(*) FROM package_products WHERE package_id = 3;

SELECT '付费套餐管理表测试完成' as test_result;
