-- =====================================================
-- 基础数据表测试脚本
-- 用途: 验证基础表结构的正确性和完整性
-- =====================================================

-- 测试表结构创建
SOURCE database/schema/01_basic_tables.sql;

-- =====================================================
-- 1. 测试门店信息表 (stores)
-- =====================================================

-- 插入测试数据
INSERT INTO stores (store_name, store_code, room_count, contact_person, contact_phone, contact_email, address, created_by) 
VALUES 
('测试酒店A', 'HOTEL_A_001', 100, '张三', '13800138001', '<EMAIL>', '北京市朝阳区测试路1号', 1),
('测试酒店B', 'HOTEL_B_002', 50, '李四', '13800138002', '<EMAIL>', '上海市浦东新区测试路2号', 1),
('测试酒店C', 'HOTEL_C_003', 200, '王五', '13800138003', '<EMAIL>', '广州市天河区测试路3号', 1);

-- 验证数据插入
SELECT COUNT(*) as store_count FROM stores WHERE is_deleted = 0;

-- 测试唯一约束
-- 以下语句应该失败（门店编码重复）
-- INSERT INTO stores (store_name, store_code, room_count) VALUES ('重复编码测试', 'HOTEL_A_001', 10);

-- 测试状态更新
UPDATE stores SET status = 'DISABLED' WHERE store_code = 'HOTEL_B_002';

-- 验证状态更新
SELECT store_name, status FROM stores WHERE store_code = 'HOTEL_B_002';

-- =====================================================
-- 2. 测试用户信息表 (users)
-- =====================================================

-- 插入测试数据
INSERT INTO users (username, real_name, password, user_type, store_id, phone, email, created_by) 
VALUES 
('admin', '系统管理员', 'encrypted_password_hash', 'ADMIN', NULL, '13900139001', '<EMAIL>', 1),
('operator01', '运营人员01', 'encrypted_password_hash', 'OPERATOR', NULL, '13900139002', '<EMAIL>', 1),
('manager_a', '酒店A管理员', 'encrypted_password_hash', 'STORE_MANAGER', 1, '13900139003', '<EMAIL>', 1),
('manager_b', '酒店B管理员', 'encrypted_password_hash', 'STORE_MANAGER', 2, '13900139004', '<EMAIL>', 1);

-- 验证数据插入
SELECT COUNT(*) as user_count FROM users WHERE is_deleted = 0;

-- 测试用户类型分布
SELECT user_type, COUNT(*) as count FROM users WHERE is_deleted = 0 GROUP BY user_type;

-- 测试门店管理员关联
SELECT u.username, u.real_name, s.store_name 
FROM users u 
LEFT JOIN stores s ON u.store_id = s.store_id 
WHERE u.user_type = 'STORE_MANAGER' AND u.is_deleted = 0;

-- =====================================================
-- 3. 测试系统配置表 (system_configs)
-- =====================================================

-- 插入测试配置
INSERT INTO system_configs (config_key, config_value, config_type, config_group, description, created_by) 
VALUES 
('system.name', 'AI运营后台付费管理系统', 'STRING', 'SYSTEM', '系统名称', 1),
('payment.timeout', '1800', 'NUMBER', 'PAYMENT', '支付超时时间（秒）', 1),
('email.enabled', 'true', 'BOOLEAN', 'EMAIL', '是否启用邮件通知', 1),
('wechat.config', '{"appid":"wx123456","secret":"secret123"}', 'JSON', 'PAYMENT', '微信支付配置', 1);

-- 验证配置数据
SELECT config_group, COUNT(*) as config_count FROM system_configs GROUP BY config_group;

-- 测试配置查询
SELECT config_key, config_value, config_type FROM system_configs WHERE config_group = 'PAYMENT';

-- =====================================================
-- 4. 数据完整性测试
-- =====================================================

-- 测试审计字段
SELECT 
    'stores' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM stores
UNION ALL
SELECT 
    'users' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM users
UNION ALL
SELECT 
    'system_configs' as table_name,
    COUNT(*) as total_records,
    COUNT(created_at) as has_created_at,
    COUNT(updated_at) as has_updated_at
FROM system_configs;

-- 测试软删除
UPDATE stores SET is_deleted = 1 WHERE store_code = 'HOTEL_C_003';
SELECT COUNT(*) as active_stores FROM stores WHERE is_deleted = 0;
SELECT COUNT(*) as deleted_stores FROM stores WHERE is_deleted = 1;

-- =====================================================
-- 5. 索引性能测试
-- =====================================================

-- 测试门店查询性能
EXPLAIN SELECT * FROM stores WHERE store_code = 'HOTEL_A_001';
EXPLAIN SELECT * FROM stores WHERE status = 'ENABLED';

-- 测试用户查询性能
EXPLAIN SELECT * FROM users WHERE username = 'admin';
EXPLAIN SELECT * FROM users WHERE user_type = 'STORE_MANAGER';

-- 测试配置查询性能
EXPLAIN SELECT * FROM system_configs WHERE config_key = 'system.name';

-- =====================================================
-- 测试完成，清理测试数据
-- =====================================================

-- 注意：在生产环境中不要执行以下清理语句
-- DELETE FROM stores;
-- DELETE FROM users;
-- DELETE FROM system_configs;

SELECT '基础数据表测试完成' as test_result;
