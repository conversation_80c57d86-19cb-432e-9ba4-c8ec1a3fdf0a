-- =====================================================
-- 发票管理表结构优化设计（去除冗余）
-- =====================================================

-- 方案一：简化为两张表
-- 1. 发票申请表（包含发票信息）
CREATE TABLE hds_invoice_application (
    id                    INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_no        VARCHAR(64) NOT NULL COMMENT '申请编号',
    hotel_code            VARCHAR(64) NOT NULL COMMENT '门店编码',
    hotel_name            VARCHAR(128) NOT NULL COMMENT '门店名称',
    
    -- 发票信息（每次申请时填写，支持不同的发票信息）
    invoice_title         VARCHAR(128) NOT NULL COMMENT '发票抬头',
    tax_number            VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email         VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content       VARCHAR(64) NOT NULL COMMENT '发票内容',
    more_info             TEXT COMMENT '更多信息/备注',
    
    -- 申请相关信息
    invoice_amount        DECIMAL(10,2) NOT NULL COMMENT '发票金额',
    bill_ids              JSON NOT NULL COMMENT '关联账单ID列表',
    bill_count            INT NOT NULL COMMENT '关联账单数量',
    
    -- 状态管理
    application_status    ENUM('PENDING','REVIEWING','APPROVED','REJECTED','INVOICING','INVOICED') 
                         DEFAULT 'PENDING' COMMENT '申请状态',
    review_result         VARCHAR(64) COMMENT '审核结果',
    review_remark         TEXT COMMENT '审核备注',
    reviewed_by           VARCHAR(64) COMMENT '审核人',
    reviewed_by_name      VARCHAR(64) COMMENT '审核人名称',
    reviewed_at           DATETIME COMMENT '审核时间',
    
    -- 开票结果
    invoice_no            VARCHAR(64) COMMENT '发票号码',
    invoice_code          VARCHAR(32) COMMENT '发票代码',
    invoice_url           VARCHAR(512) COMMENT '发票文件URL',
    invoiced_at           DATETIME COMMENT '开票时间',
    
    -- 审计字段
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_hotel_code (hotel_code),
    KEY idx_application_status (application_status),
    KEY idx_created_at (created_at),
    KEY idx_invoice_no (invoice_no),
    KEY idx_hotel_status_time (hotel_code, application_status, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票申请表';

-- 2. 发票明细表（记录具体账单信息）
CREATE TABLE hds_invoice_detail (
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_id    INT NOT NULL COMMENT '发票申请ID',
    bill_id           INT NOT NULL COMMENT '账单ID',
    bill_no           VARCHAR(64) NOT NULL COMMENT '账单编号',
    order_no          VARCHAR(64) NOT NULL COMMENT '订单编号',
    package_name      VARCHAR(64) NOT NULL COMMENT '套餐名称',
    service_period    VARCHAR(32) NOT NULL COMMENT '服务周期',
    service_start_time DATETIME NOT NULL COMMENT '服务开始时间',
    service_end_time  DATETIME NOT NULL COMMENT '服务结束时间',
    bill_amount       DECIMAL(10,2) NOT NULL COMMENT '账单金额',
    created_at        DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    row_status        INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    KEY idx_application_id (application_id),
    KEY idx_bill_id (bill_id),
    KEY idx_order_no (order_no),
    UNIQUE KEY uk_application_bill (application_id, bill_id),
    FOREIGN KEY fk_invoice_application (application_id) REFERENCES hds_invoice_application(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票明细表';

-- =====================================================
-- 方案二：保留预设信息表但简化设计
-- =====================================================

-- 如果需要预设功能，可以保留一个简化的预设表
CREATE TABLE hds_invoice_template (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    hotel_code      VARCHAR(64) NOT NULL COMMENT '门店编码',
    template_name   VARCHAR(64) NOT NULL COMMENT '模板名称',
    invoice_title   VARCHAR(128) NOT NULL COMMENT '发票抬头',
    tax_number      VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email   VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content VARCHAR(64) DEFAULT '服务费' COMMENT '发票内容',
    is_default      TINYINT DEFAULT 0 COMMENT '是否默认模板',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    KEY idx_hotel_code (hotel_code),
    KEY idx_is_default (is_default),
    UNIQUE KEY uk_hotel_template (hotel_code, template_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票模板表';

-- =====================================================
-- 优化后的表关系图
-- =====================================================

/*
方案一：两张表设计
┌─────────────────────────────────────┐
│      hds_invoice_application        │  ◄── 包含所有发票信息
│      (发票申请表)                    │      每次申请独立填写
│                                    │
│ • 申请信息                          │
│ • 发票信息                          │
│ • 状态管理                          │
│ • 开票结果                          │
└─────────────┬───────────────────────┘
              │ 1:N
              ▼
┌─────────────────────────────────────┐
│       hds_invoice_detail            │
│       (发票明细表)                   │
│                                    │
│ • 账单明细                          │
│ • 服务信息                          │
└─────────────────────────────────────┘

方案二：三张表设计（保留模板功能）
┌─────────────────────────────────────┐
│      hds_invoice_template           │  ◄── 可选的预设模板
│      (发票模板表)                    │      支持多个模板
└─────────────┬───────────────────────┘
              │ 参考关系（非强制）
              ▼
┌─────────────────────────────────────┐
│      hds_invoice_application        │  ◄── 发票申请主表
│      (发票申请表)                    │
└─────────────┬───────────────────────┘
              │ 1:N
              ▼
┌─────────────────────────────────────┐
│       hds_invoice_detail            │
│       (发票明细表)                   │
└─────────────────────────────────────┘
*/

-- =====================================================
-- 示例数据
-- =====================================================

-- 方案一示例数据
INSERT INTO hds_invoice_application (
    application_no, hotel_code, hotel_name, invoice_title, tax_number, 
    receive_email, invoice_content, invoice_amount, bill_ids, bill_count, 
    application_status
) VALUES
('INV_20240115_000001', 'HOTEL_001', '北京某某酒店', '北京某某酒店有限公司', '91110000000000000X', 
 '<EMAIL>', '技术服务费', 2400.00, '[1,2,3]', 3, 'PENDING'),
('INV_20240115_000002', 'HOTEL_001', '北京某某酒店', '北京某某酒店有限公司', '91110000000000000X', 
 '<EMAIL>', '软件服务费', 1800.00, '[4,5]', 2, 'INVOICED');

-- 方案二示例数据（如果使用模板）
INSERT INTO hds_invoice_template (
    hotel_code, template_name, invoice_title, tax_number, receive_email, 
    invoice_content, is_default
) VALUES
('HOTEL_001', '默认发票信息', '北京某某酒店有限公司', '91110000000000000X', '<EMAIL>', '技术服务费', 1),
('HOTEL_001', '软件服务发票', '北京某某酒店有限公司', '91110000000000000X', '<EMAIL>', '软件服务费', 0);

-- =====================================================
-- 查询示例
-- =====================================================

-- 查询门店的所有发票申请
SELECT 
    application_no,
    invoice_title,
    invoice_amount,
    bill_count,
    application_status,
    created_at
FROM hds_invoice_application
WHERE hotel_code = 'HOTEL_001'
ORDER BY created_at DESC;

-- 查询发票申请的账单明细
SELECT 
    ia.application_no,
    ia.invoice_amount,
    id.bill_no,
    id.package_name,
    id.bill_amount
FROM hds_invoice_application ia
JOIN hds_invoice_detail id ON ia.id = id.application_id
WHERE ia.application_no = 'INV_20240115_000001';

-- 如果使用模板，查询门店的发票模板
SELECT 
    template_name,
    invoice_title,
    tax_number,
    receive_email,
    is_default
FROM hds_invoice_template
WHERE hotel_code = 'HOTEL_001'
ORDER BY is_default DESC, template_name;
