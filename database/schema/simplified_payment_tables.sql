-- =====================================================
-- AI运营后台付费管理系统 - 精简版表结构设计
-- 创建时间: 2025-01-10
-- 描述: 严格按照需求文档设计，去除不必要的字段
-- =====================================================

-- =====================================================
-- 1. 付费套餐主表 (hds_payment_package)
-- 描述: 存储付费套餐的基本信息
-- =====================================================
create table hds_payment_package
(
    id              int auto_increment comment '主键'
        primary key,
    package_code    varchar(64)                           not null comment '套餐编码，唯一标识',
    package_name    varchar(64)                           not null comment '套餐名称，唯一',
    payment_mode    tinyint                               not null comment '付费方式：0-按房间数量收费，1-按门店收费',
    discount_mode   tinyint                               not null comment '优惠方式：1-折扣，2-一口价',
    status          tinyint     default 1                 not null comment '状态：1-启用，0-停用',
    is_recommended  tinyint     default 0                 not null comment '是否推荐：1-是，0-否',
    created_by      varchar(64) default '1'               not null comment '创建人id',
    created_by_name varchar(64) default '1'               not null comment '创建人名称',
    updated_by      varchar(64) default '1'               not null comment '修改人id',
    updated_by_name varchar(64) default '1'               not null comment '修改人名称',
    created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_package_code
        unique (package_code),
    constraint uk_package_name
        unique (package_name)
)
    comment '付费套餐主表';

create index idx_payment_mode on hds_payment_package (payment_mode);
create index idx_discount_mode on hds_payment_package (discount_mode);
create index idx_status on hds_payment_package (status);
create index idx_is_recommended on hds_payment_package (is_recommended);

-- =====================================================
-- 2. 付费产品表 (hds_payment_product)
-- 描述: 存储套餐包含的产品信息
-- =====================================================
create table hds_payment_product
(
    id                  int auto_increment comment '主键'
        primary key,
    package_id          int                                   not null comment '套餐ID，关联hds_payment_package.id',
    product_name        varchar(64)                           not null comment '产品名称',
    product_description json                                  null comment '产品描述JSON数组，最多10条，每条最大50字符',
    sort_order          int         default 0                 not null comment '产品在套餐中的排序',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int         default 1                 not null comment '记录状态(1:有效, 0:无效)'
)
    comment '付费产品表';

create index idx_package_id on hds_payment_product (package_id);
create index idx_sort_order on hds_payment_product (sort_order);

-- =====================================================
-- 3. 产品定价表 (hds_product_pricing)
-- 描述: 存储产品的定价信息，按周期类型规范化设计
-- =====================================================
create table hds_product_pricing
(
    id              int auto_increment comment '主键'
        primary key,
    product_id      int                                   not null comment '产品ID，关联hds_payment_product.id',
    period_type     enum('MONTHLY','QUARTERLY','YEARLY')  not null comment '周期类型：MONTHLY-月度，QUARTERLY-季度，YEARLY-年度',
    market_price    decimal(10,2)                         not null comment '门市价',
    discount_price  decimal(10,2)                         null comment '优惠价（一口价模式使用）',
    discount_rate   decimal(5,2)                          null comment '折扣比例，百分比（折扣模式使用）',
    final_price     decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    created_by      varchar(64) default '1'               not null comment '创建人id',
    created_by_name varchar(64) default '1'               not null comment '创建人名称',
    updated_by      varchar(64) default '1'               not null comment '修改人id',
    updated_by_name varchar(64) default '1'               not null comment '修改人名称',
    created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_product_period
        unique (product_id, period_type)
)
    comment '产品定价表';

create index idx_product_id on hds_product_pricing (product_id);
create index idx_period_type on hds_product_pricing (period_type);
create index idx_final_price on hds_product_pricing (final_price);

-- =====================================================
-- 4. 门店套餐关联表 (hds_hotel_package)
-- 描述: 存储门店与套餐的关联关系
-- =====================================================
create table hds_hotel_package
(
    id                  int auto_increment comment '主键'
        primary key,
    hotel_package_code  varchar(64)                           not null comment '门店套餐编码，系统自动生成',
    hotel_code          varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    package_id          int                                   not null comment '套餐ID，关联hds_payment_package.id',
    status              tinyint     default 0                 not null comment '状态：1-启用，0-停用',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_package_code
        unique (hotel_package_code),
    constraint uk_hotel_package
        unique (hotel_code, package_id)
)
    comment '门店套餐关联表';

create index idx_hotel_code on hds_hotel_package (hotel_code);
create index idx_package_id_hotel on hds_hotel_package (package_id);
create index idx_status_hotel on hds_hotel_package (status);

-- =====================================================
-- 5. 门店个性化定价表 (hds_hotel_product_pricing)
-- 描述: 存储门店级别的个性化定价信息
-- =====================================================
create table hds_hotel_product_pricing
(
    id                    int auto_increment comment '主键'
        primary key,
    hotel_package_id      int                                   not null comment '门店套餐ID，关联hds_hotel_package.id',
    product_id            int                                   not null comment '产品ID，关联hds_payment_product.id',
    period_type           enum('MONTHLY','QUARTERLY','YEARLY')  not null comment '周期类型：MONTHLY-月度，QUARTERLY-季度，YEARLY-年度',
    custom_market_price   decimal(10,2)                         null comment '自定义门市价，为空则使用标准价格',
    custom_discount_price decimal(10,2)                         null comment '自定义优惠价，为空则使用标准价格',
    custom_discount_rate  decimal(5,2)                          null comment '自定义折扣比例，为空则使用标准比例',
    final_price           decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    is_custom_pricing     tinyint     default 0                 not null comment '是否使用自定义定价：0-使用标准价格，1-使用自定义价格',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_product_period
        unique (hotel_package_id, product_id, period_type)
)
    comment '门店个性化定价表';

create index idx_hotel_package_id on hds_hotel_product_pricing (hotel_package_id);
create index idx_product_id_hotel on hds_hotel_product_pricing (product_id);
create index idx_period_type_hotel on hds_hotel_product_pricing (period_type);
create index idx_final_price_hotel on hds_hotel_product_pricing (final_price);
create index idx_is_custom_pricing on hds_hotel_product_pricing (is_custom_pricing);

-- =====================================================
-- 表结构创建完成
-- =====================================================
