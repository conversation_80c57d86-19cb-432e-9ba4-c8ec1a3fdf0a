-- =====================================================
-- 发票管理完整表结构设计（基于需求分析）
-- =====================================================

-- 1. 发票信息表（门店预设发票信息）
CREATE TABLE hds_invoice_info (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    hotel_code      VARCHAR(64) NOT NULL COMMENT '门店编码',
    invoice_title   VARCHAR(128) NOT NULL COMMENT '发票抬头', -- 修正字段名
    tax_number      VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email   VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content VARCHAR(64) DEFAULT '服务费' COMMENT '发票内容',
    invoice_type    ENUM('NORMAL','SPECIAL') DEFAULT 'NORMAL' COMMENT '发票类型：普通发票/专用发票', -- 新增
    more_info       TEXT COMMENT '更多信息/备注',
    created_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    UNIQUE KEY uk_hotel_code (hotel_code),
    KEY idx_tax_number (tax_number),
    KEY idx_invoice_title (invoice_title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票信息表';

-- 2. 发票申请表（完整版）
CREATE TABLE hds_invoice_application (
    id                    BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_no        VARCHAR(64) NOT NULL COMMENT '申请编号',
    hotel_code            VARCHAR(64) NOT NULL COMMENT '门店编码',
    hotel_name            VARCHAR(128) NOT NULL COMMENT '门店名称', -- 新增
    
    -- 发票信息（每次申请时的具体信息）
    invoice_title         VARCHAR(128) NOT NULL COMMENT '发票抬头', -- 新增
    tax_number            VARCHAR(32) NOT NULL COMMENT '纳税人识别号', -- 新增
    receive_email         VARCHAR(128) NOT NULL COMMENT '接收邮箱', -- 新增
    invoice_content       VARCHAR(64) NOT NULL COMMENT '发票内容', -- 新增
    invoice_type          ENUM('NORMAL','SPECIAL') DEFAULT 'NORMAL' COMMENT '发票类型', -- 新增
    more_info             TEXT COMMENT '更多信息/备注', -- 新增
    
    -- 申请相关信息
    invoice_amount        DECIMAL(10,2) NOT NULL COMMENT '发票金额',
    bill_count            INT NOT NULL COMMENT '关联账单数量', -- 新增
    
    -- 状态管理（修正数据类型）
    application_status    TINYINT DEFAULT 0 COMMENT '申请状态：0-待审核，1-审核中，2-已开票，3-已驳回',
    review_result         VARCHAR(64) COMMENT '审核结果', -- 新增
    review_remark         TEXT COMMENT '审核备注',
    reviewed_by           VARCHAR(64) COMMENT '审核人',
    reviewed_by_name      VARCHAR(64) COMMENT '审核人名称',
    reviewed_at           DATETIME COMMENT '审核时间',
    
    -- 开票结果
    invoice_no            VARCHAR(64) COMMENT '发票号码',
    invoice_code          VARCHAR(32) COMMENT '发票代码',
    invoice_url           VARCHAR(512) COMMENT '发票文件URL',
    invoiced_at           DATETIME COMMENT '开票时间',
    
    -- 审计字段
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    -- 索引（新增）
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_hotel_code (hotel_code),
    KEY idx_application_status (application_status),
    KEY idx_created_at (created_at),
    KEY idx_invoice_no (invoice_no),
    KEY idx_hotel_status_time (hotel_code, application_status, created_at),
    KEY idx_tax_number (tax_number),
    KEY idx_invoice_title (invoice_title)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票申请表';

-- 3. 发票明细表（完整版）
CREATE TABLE hds_invoice_detail (
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_id    BIGINT NOT NULL COMMENT '发票申请ID', -- 修正数据类型
    bill_id           INT NOT NULL COMMENT '账单ID',
    bill_no           VARCHAR(64) NOT NULL COMMENT '账单编号',
    order_no          VARCHAR(64) NOT NULL COMMENT '订单编号',
    package_name      VARCHAR(64) NOT NULL COMMENT '套餐名称',
    service_period    VARCHAR(32) NOT NULL COMMENT '服务周期', -- 新增
    service_start_time DATETIME NOT NULL COMMENT '服务开始时间', -- 新增
    service_end_time  DATETIME NOT NULL COMMENT '服务结束时间', -- 新增
    bill_amount       DECIMAL(10,2) NOT NULL COMMENT '账单金额',
    
    -- 审计字段
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    
    -- 索引和约束
    KEY idx_application_id (application_id),
    KEY idx_bill_id (bill_id),
    KEY idx_order_no (order_no),
    KEY idx_service_time (service_start_time, service_end_time), -- 新增
    UNIQUE KEY uk_application_bill (application_id, bill_id), -- 新增唯一约束
    FOREIGN KEY fk_invoice_application (application_id) REFERENCES hds_invoice_application(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票明细表';

-- =====================================================
-- 补充：发票状态变更日志表（可选）
-- =====================================================
CREATE TABLE hds_invoice_status_log (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_id  BIGINT NOT NULL COMMENT '发票申请ID',
    old_status      TINYINT COMMENT '原状态',
    new_status      TINYINT NOT NULL COMMENT '新状态',
    change_reason   VARCHAR(128) COMMENT '变更原因',
    changed_by      VARCHAR(64) NOT NULL COMMENT '操作人',
    changed_by_name VARCHAR(64) NOT NULL COMMENT '操作人名称',
    changed_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '变更时间',
    remark          TEXT COMMENT '备注',
    
    KEY idx_application_id (application_id),
    KEY idx_changed_at (changed_at),
    KEY idx_new_status (new_status),
    FOREIGN KEY fk_invoice_app_log (application_id) REFERENCES hds_invoice_application(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票状态变更日志表';

-- =====================================================
-- 视图：发票申请汇总信息
-- =====================================================
CREATE VIEW v_invoice_application_summary AS
SELECT 
    ia.id,
    ia.application_no,
    ia.hotel_code,
    ia.hotel_name,
    ia.invoice_title,
    ia.tax_number,
    ia.invoice_amount,
    ia.bill_count,
    ia.application_status,
    CASE ia.application_status
        WHEN 0 THEN '待审核'
        WHEN 1 THEN '审核中'
        WHEN 2 THEN '已开票'
        WHEN 3 THEN '已驳回'
        ELSE '未知'
    END as status_text,
    ia.created_at as application_time,
    ia.invoiced_at,
    ia.invoice_no,
    ia.invoice_url,
    COUNT(id_detail.id) as actual_bill_count,
    SUM(id_detail.bill_amount) as actual_invoice_amount
FROM hds_invoice_application ia
LEFT JOIN hds_invoice_detail id_detail ON ia.id = id_detail.application_id
WHERE ia.row_status = 1
GROUP BY ia.id;

-- =====================================================
-- 示例数据
-- =====================================================

-- 发票信息表示例
INSERT INTO hds_invoice_info (hotel_code, invoice_title, tax_number, receive_email, invoice_content, invoice_type) VALUES
('HOTEL_001', '北京某某酒店有限公司', '91110000000000000X', '<EMAIL>', '技术服务费', 'NORMAL'),
('HOTEL_002', '上海某某大酒店', '91310000000000000Y', '<EMAIL>', '软件服务费', 'NORMAL');

-- 发票申请表示例
INSERT INTO hds_invoice_application (
    application_no, hotel_code, hotel_name, invoice_title, tax_number, 
    receive_email, invoice_content, invoice_amount, bill_count, application_status
) VALUES
('INV_20240115_000001', 'HOTEL_001', '北京某某酒店', '北京某某酒店有限公司', '91110000000000000X', 
 '<EMAIL>', '技术服务费', 2400.00, 3, 0),
('INV_20240115_000002', 'HOTEL_002', '上海某某大酒店', '上海某某大酒店', '91310000000000000Y', 
 '<EMAIL>', '软件服务费', 1800.00, 2, 2);

-- 发票明细表示例
INSERT INTO hds_invoice_detail (
    application_id, bill_id, bill_no, order_no, package_name, 
    service_period, service_start_time, service_end_time, bill_amount
) VALUES
(1, 1, 'BILL_20240101_000001', 'ORD_20240101_000001', 'AI客服基础套餐', 
 'MONTHLY', '2024-01-01 00:00:00', '2024-01-31 23:59:59', 800.00),
(1, 2, 'BILL_20240201_000002', 'ORD_20240201_000002', 'AI客服基础套餐', 
 'MONTHLY', '2024-02-01 00:00:00', '2024-02-29 23:59:59', 800.00),
(1, 3, 'BILL_20240301_000003', 'ORD_20240301_000003', 'AI客服基础套餐', 
 'MONTHLY', '2024-03-01 00:00:00', '2024-03-31 23:59:59', 800.00);
