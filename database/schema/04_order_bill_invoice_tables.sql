-- =====================================================
-- AI运营后台付费管理系统 - 订单账单发票表结构
-- 创建时间: 2025-01-10
-- 描述: 包含交易订单、账单管理、发票管理相关表结构，优化业务逻辑和数据设计
-- =====================================================

-- =====================================================
-- 1. 交易订单表 (hds_payment_order)
-- 描述: 存储门店购买套餐的订单信息，订单粒度为套餐级别
-- =====================================================
create table hds_payment_order
(
    id                    int auto_increment comment '主键'
        primary key,
    order_no              varchar(64)                           not null comment '订单编号，系统自动生成',
    hotel_code            varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    hotel_package_id      int                                   not null comment '门店套餐ID，关联hds_hotel_package.id',
    purchase_period       enum('MONTHLY','QUARTERLY','YEARLY') not null comment '购买周期',
    room_count            int                                   null comment '房间数量（按房间收费时记录）',
    original_amount       decimal(10,2)                         not null comment '原价金额',
    discount_amount       decimal(10,2)                         not null default 0.00 comment '优惠金额',
    order_amount          decimal(10,2)                         not null comment '订单金额（原价-优惠）',
    service_start_time    datetime                              null comment '服务开始时间',
    service_end_time      datetime                              null comment '服务结束时间',
    order_status          enum('PENDING','PAID','CANCELLED','EXPIRED') default 'PENDING' comment '订单状态',
    payment_method        enum('WECHAT','ALIPAY')               null comment '支付方式',
    paid_at               datetime                              null comment '支付时间',
    expire_at             datetime                              null comment '订单过期时间',
    remark                text                                  null comment '备注信息',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_order_no
        unique (order_no)
)
    comment '交易订单表';

create index idx_hotel_code on hds_payment_order (hotel_code);
create index idx_hotel_package_id on hds_payment_order (hotel_package_id);
create index idx_order_status on hds_payment_order (order_status);
create index idx_purchase_period on hds_payment_order (purchase_period);
create index idx_created_at on hds_payment_order (created_at);
create index idx_service_time on hds_payment_order (service_start_time, service_end_time);

-- =====================================================
-- 2. 订单明细表 (hds_order_detail)
-- 描述: 记录订单包含的具体产品信息，支持订单级别的产品明细追踪
-- =====================================================
create table hds_order_detail
(
    id                    int auto_increment comment '主键'
        primary key,
    order_id              int                                   not null comment '订单ID，关联hds_payment_order.id',
    product_id            int                                   not null comment '产品ID，关联hds_payment_product.id',
    product_name          varchar(64)                           not null comment '产品名称（冗余存储，保证历史数据准确性）',
    period_type           enum('MONTHLY','QUARTERLY','YEARLY') not null comment '周期类型',
    unit_price            decimal(10,2)                         not null comment '单价',
    quantity              int                                   not null default 1 comment '数量',
    amount                decimal(10,2)                         not null comment '小计金额',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)'
)
    comment '订单明细表';

create index idx_order_id on hds_order_detail (order_id);
create index idx_product_id on hds_order_detail (product_id);
create index idx_period_type on hds_order_detail (period_type);

-- =====================================================
-- 3. 账单表 (hds_payment_bill)
-- 描述: 存储支付成功后的账单信息，记录实际交易流水
-- =====================================================
create table hds_payment_bill
(
    id                    int auto_increment comment '主键'
        primary key,
    bill_no               varchar(64)                           not null comment '账单编号，系统自动生成',
    transaction_no        varchar(64)                           not null comment '流水号（从支付平台获取）',
    order_id              int                                   not null comment '订单ID，关联hds_payment_order.id',
    hotel_code            varchar(64)                           not null comment '酒店编码',
    payment_amount        decimal(10,2)                         not null comment '支付金额',
    payment_method        enum('WECHAT','ALIPAY')               not null comment '支付方式',
    transaction_status    enum('SUCCESS','REFUNDED','FAILED')  default 'SUCCESS' comment '交易状态',
    transaction_at        datetime                              not null comment '交易完成时间',
    refund_amount         decimal(10,2)                         null comment '退款金额',
    refund_at             datetime                              null comment '退款时间',
    refund_reason         varchar(255)                          null comment '退款原因',
    invoice_status        enum('NONE','APPLIED','INVOICED')    default 'NONE' comment '开票状态',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_transaction_no
        unique (transaction_no),
    constraint uk_bill_no
        unique (bill_no)
)
    comment '账单表';

create index idx_order_id on hds_payment_bill (order_id);
create index idx_hotel_code on hds_payment_bill (hotel_code);
create index idx_transaction_status on hds_payment_bill (transaction_status);
create index idx_transaction_at on hds_payment_bill (transaction_at);
create index idx_invoice_status on hds_payment_bill (invoice_status);

-- =====================================================
-- 4. 发票信息表 (hds_invoice_info)
-- 描述: 保存门店的发票信息，支持发票信息的预设和管理
-- =====================================================
create table hds_invoice_info
(
    id                    int auto_increment comment '主键'
        primary key,
    hotel_code            varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    invoice_title         varchar(255)                          not null comment '发票抬头',
    tax_number            varchar(64)                           not null comment '纳税人识别号',
    receive_email         varchar(128)                          not null comment '接收邮箱',
    invoice_content       varchar(64)   default '服务费'         not null comment '发票内容',
    more_info             text                                  null comment '更多信息（备注）',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_code
        unique (hotel_code)
)
    comment '发票信息表';

create index idx_tax_number on hds_invoice_info (tax_number);

-- =====================================================
-- 5. 发票申请表 (hds_invoice_application)
-- 描述: 存储门店的发票申请信息，支持批量开票申请
-- =====================================================
create table hds_invoice_application
(
    id                    int auto_increment comment '主键'
        primary key,
    application_no        varchar(64)                           not null comment '申请编号，系统自动生成',
    hotel_code            varchar(64)                           not null comment '酒店编码',
    invoice_title         varchar(255)                          not null comment '发票抬头',
    tax_number            varchar(64)                           not null comment '纳税人识别号',
    invoice_content       varchar(64)   default '服务费'         not null comment '发票内容',
    invoice_amount        decimal(10,2)                         not null comment '发票金额',
    receive_email         varchar(128)                          not null comment '接收邮箱',
    more_info             text                                  null comment '更多信息（备注）',
    bill_count            int                                   not null comment '关联账单数量',
    application_status    enum('PENDING','REVIEWING','INVOICED','REJECTED') default 'PENDING' comment '申请状态',
    reject_reason         text                                  null comment '驳回原因',
    invoice_time          datetime                              null comment '开票时间',
    invoice_file_url      varchar(255)                          null comment '发票文件URL',
    created_by            varchar(64)   default '1'             not null comment '创建人id',
    created_by_name       varchar(64)   default '1'             not null comment '创建人名称',
    updated_by            varchar(64)   default '1'             not null comment '修改人id',
    updated_by_name       varchar(64)   default '1'             not null comment '修改人名称',
    created_at            datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int           default 1               not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_application_no
        unique (application_no)
)
    comment '发票申请表';

create index idx_hotel_code on hds_invoice_application (hotel_code);
create index idx_application_status on hds_invoice_application (application_status);
create index idx_created_at on hds_invoice_application (created_at);
create index idx_invoice_time on hds_invoice_application (invoice_time);

-- =====================================================
-- 6. 发票明细表 (hds_invoice_detail)
-- 描述: 记录发票申请包含的具体账单明细信息
-- =====================================================
create table hds_invoice_detail
(
    id                    int auto_increment comment '主键'
        primary key,
    application_id        int                                   not null comment '发票申请ID，关联hds_invoice_application.id',
    bill_id               int                                   not null comment '账单ID，关联hds_payment_bill.id',
    order_id              int                                   not null comment '订单ID，关联hds_payment_order.id',
    product_name          varchar(64)                           not null comment '产品名称',
    service_period        varchar(32)                           not null comment '服务周期',
    service_start_time    datetime                              not null comment '服务开始时间',
    service_end_time      datetime                              not null comment '服务结束时间',
    bill_amount           decimal(10,2)                         not null comment '账单金额',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)'
)
    comment '发票明细表';

create index idx_application_id on hds_invoice_detail (application_id);
create index idx_bill_id on hds_invoice_detail (bill_id);
create index idx_order_id on hds_invoice_detail (order_id);
create index idx_service_time on hds_invoice_detail (service_start_time, service_end_time);

-- =====================================================
-- 表结构创建完成
-- =====================================================
