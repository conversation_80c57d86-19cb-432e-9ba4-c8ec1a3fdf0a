-- =====================================================
-- AI运营后台付费管理系统 - 门店付费设置表结构
-- 创建时间: 2025-01-10
-- 描述: 包含门店套餐关联表和门店个性化定价表，支持门店级别的定制化付费配置
-- =====================================================

-- =====================================================
-- 1. 门店套餐关联表 (hds_store_packages)
-- 描述: 存储门店与套餐的关联关系，支持门店级别的套餐配置
-- =====================================================
create table hds_store_packages
(
    id                  int auto_increment comment '主键'
        primary key,
    store_package_code  varchar(32)                           not null comment '门店套餐编码，系统自动生成，格式：SP_YYYYMMDD_XXXX',
    hotel_code          varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    package_id          int                                   not null comment '套餐ID，关联hds_payment_packages.id',
    status              enum('ENABLED','DISABLED','DELETED') not null default 'DISABLED' comment '状态：ENABLED-启用，DISABLED-停用，DELETED-删除',
    effective_date      datetime                              null comment '生效时间',
    expire_date         datetime                              null comment '失效时间',
    remark              text                                  null comment '备注信息',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_store_package_code
        unique (store_package_code),
    constraint uk_hotel_package
        unique (hotel_code, package_id)
)
    comment '门店套餐关联表';

create index idx_hotel_code
    on hds_store_packages (hotel_code);

create index idx_package_id
    on hds_store_packages (package_id);

create index idx_status
    on hds_store_packages (status);

create index idx_effective_date
    on hds_store_packages (effective_date);

-- =====================================================
-- 2. 门店个性化定价表 (hds_store_package_pricing)
-- 描述: 存储门店级别的个性化定价信息，支持覆盖默认套餐价格
-- =====================================================
create table hds_store_package_pricing
(
    id                      int auto_increment comment '主键'
        primary key,
    store_package_id        int                                   not null comment '门店套餐ID，关联hds_store_packages.id',
    product_id              int                                   not null comment '产品ID，关联hds_package_products.id',
    period_type             enum('MONTHLY','QUARTERLY','YEARLY')  not null comment '周期类型：MONTHLY-月度，QUARTERLY-季度，YEARLY-年度',
    custom_market_price     decimal(10,2)                         null comment '自定义门市价，为空则使用默认价格',
    custom_discount_price   decimal(10,2)                         null comment '自定义优惠价，为空则使用默认价格',
    custom_discount_rate    decimal(5,2)                          null comment '自定义折扣比例，百分比（如10.50表示10.5%）',
    custom_fixed_price      decimal(10,2)                         null comment '自定义一口价，为空则使用默认价格',
    final_price             decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    is_custom_pricing       tinyint                               not null default 0 comment '是否使用自定义定价：0-使用默认价格，1-使用自定义价格',
    price_calculation_note  varchar(255)                          null comment '价格计算说明',
    created_by              varchar(64) default '1'               not null comment '创建人id',
    created_by_name         varchar(64) default '1'               not null comment '创建人名称',
    updated_by              varchar(64) default '1'               not null comment '修改人id',
    updated_by_name         varchar(64) default '1'               not null comment '修改人名称',
    created_at              datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at              datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status              int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_store_product_period
        unique (store_package_id, product_id, period_type)
)
    comment '门店个性化定价表';

create index idx_store_package_id
    on hds_store_package_pricing (store_package_id);

create index idx_product_id
    on hds_store_package_pricing (product_id);

create index idx_period_type
    on hds_store_package_pricing (period_type);

create index idx_final_price
    on hds_store_package_pricing (final_price);

create index idx_is_custom_pricing
    on hds_store_package_pricing (is_custom_pricing);

-- =====================================================
-- 3. 门店套餐使用记录表 (hds_store_package_usage)
-- 描述: 记录门店套餐的使用历史，支持套餐变更追踪
-- =====================================================
create table hds_store_package_usage
(
    id                  int auto_increment comment '主键'
        primary key,
    hotel_code          varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    store_package_id    int                                   not null comment '门店套餐ID，关联hds_store_packages.id',
    usage_type          enum('ACTIVATE','DEACTIVATE','MODIFY','DELETE') not null comment '使用类型：ACTIVATE-激活，DEACTIVATE-停用，MODIFY-修改，DELETE-删除',
    usage_date          datetime                              not null comment '操作时间',
    operator_id         varchar(64)                           not null comment '操作人ID',
    operator_name       varchar(64)                           not null comment '操作人名称',
    operation_reason    varchar(255)                          null comment '操作原因',
    before_status       varchar(50)                           null comment '操作前状态',
    after_status        varchar(50)                           null comment '操作后状态',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int                                   null comment '记录状态(1:有效, 0:无效)'
)
    comment '门店套餐使用记录表';

create index idx_hotel_code_usage
    on hds_store_package_usage (hotel_code);

create index idx_store_package_id_usage
    on hds_store_package_usage (store_package_id);

create index idx_usage_type
    on hds_store_package_usage (usage_type);

create index idx_usage_date
    on hds_store_package_usage (usage_date);

create index idx_operator_id
    on hds_store_package_usage (operator_id);

-- =====================================================
-- 表结构创建完成
-- =====================================================
