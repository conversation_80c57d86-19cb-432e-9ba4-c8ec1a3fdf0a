-- =====================================================
-- AI运营后台付费管理系统 - 基础数据表结构
-- 创建时间: 2025-01-10
-- 描述: 基于现有门店表(hds_hotel_info)和员工表(hds_employee)，补充付费管理所需的基础配置表
-- 注意: 门店信息使用现有的hds_hotel_info表，用户信息使用现有的hds_employee表
-- =====================================================



-- =====================================================
-- 说明: 现有表结构映射关系
-- hds_hotel_info -> 门店信息表 (使用hotel_code作为门店标识，total_room作为房间数量)
-- hds_employee -> 员工信息表 (使用username作为用户标识)
-- hds_employee_hotel -> 员工门店角色关联表
-- =====================================================

-- =====================================================
-- 1. 付费管理扩展配置表 (hds_payment_configs)
-- 描述: 存储付费管理相关的系统配置，补充现有系统配置
-- =====================================================
create table hds_payment_configs
(
    id              int auto_increment comment '主键'
        primary key,
    config_key      varchar(100)                          not null comment '配置键，唯一标识',
    config_value    text                                  null comment '配置值',
    config_type     enum('STRING','NUMBER','BOOLEAN','JSON') not null default 'STRING' comment '配置类型',
    config_group    varchar(50)  default 'PAYMENT'        null comment '配置分组',
    description     varchar(255)                          null comment '配置描述',
    is_system       tinyint                               not null default 0 comment '是否系统配置：0-否，1-是',
    status          tinyint                               not null default 1 comment '状态：1-启用，0-停用',
    created_by      varchar(64) default '1'               not null comment '创建人id',
    created_by_name varchar(64) default '1'               not null comment '创建人名称',
    updated_by      varchar(64) default '1'               not null comment '修改人id',
    updated_by_name varchar(64) default '1'               not null comment '修改人名称',
    created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status      int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_config_key
        unique (config_key)
)
    comment '付费管理配置表';

create index idx_config_group
    on hds_payment_configs (config_group);

create index idx_status
    on hds_payment_configs (status);

-- =====================================================
-- 2. 门店付费扩展信息表 (hds_hotel_payment_info)
-- 描述: 扩展现有门店表，存储付费管理相关的门店信息
-- =====================================================
create table hds_hotel_payment_info
(
    id                  int auto_increment comment '主键'
        primary key,
    hotel_code          varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    payment_status      tinyint                               not null default 0 comment '付费状态：0-未付费，1-已付费，2-已过期',
    current_package_id  int                                   null comment '当前使用的套餐ID',
    service_start_time  datetime                              null comment '服务开始时间',
    service_end_time    datetime                              null comment '服务结束时间',
    auto_renew          tinyint                               not null default 0 comment '是否自动续费：0-否，1-是',
    payment_method      varchar(20)                           null comment '支付方式：WECHAT-微信，ALIPAY-支付宝',
    total_paid_amount   decimal(10,2)                         not null default 0.00 comment '累计支付金额',
    last_payment_time   datetime                              null comment '最后支付时间',
    remark              text                                  null comment '备注信息',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_code
        unique (hotel_code)
)
    comment '门店付费扩展信息表';

create index idx_payment_status
    on hds_hotel_payment_info (payment_status);

create index idx_package_id
    on hds_hotel_payment_info (current_package_id);

create index idx_service_time
    on hds_hotel_payment_info (service_start_time, service_end_time);

-- =====================================================
-- 3. 员工付费权限扩展表 (hds_employee_payment_role)
-- 描述: 扩展现有员工表，存储付费管理相关的权限信息
-- =====================================================
create table hds_employee_payment_role
(
    id                  int auto_increment comment '主键'
        primary key,
    employee_id         int                                   not null comment '员工ID，关联hds_employee.id',
    hotel_code          varchar(64)                           null comment '酒店编码，关联hds_hotel_info.hotel_code',
    payment_role        enum('PAYMENT_ADMIN','PAYMENT_OPERATOR','PAYMENT_VIEWER') not null comment '付费权限角色：PAYMENT_ADMIN-付费管理员，PAYMENT_OPERATOR-付费操作员，PAYMENT_VIEWER-付费查看员',
    can_purchase        tinyint                               not null default 0 comment '是否可以购买套餐：0-否，1-是',
    can_view_bills      tinyint                               not null default 1 comment '是否可以查看账单：0-否，1-是',
    can_apply_invoice   tinyint                               not null default 0 comment '是否可以申请发票：0-否，1-是',
    status              tinyint                               not null default 1 comment '状态：1-正常，0-停用',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_employee_hotel
        unique (employee_id, hotel_code)
)
    comment '员工付费权限扩展表';

create index idx_employee_id
    on hds_employee_payment_role (employee_id);

create index idx_hotel_code
    on hds_employee_payment_role (hotel_code);

create index idx_payment_role
    on hds_employee_payment_role (payment_role);

-- =====================================================
-- 表结构创建完成
-- =====================================================
