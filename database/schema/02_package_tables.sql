-- =====================================================
-- AI运营后台付费管理系统 - 付费套餐管理表结构
-- 创建时间: 2025-01-10
-- 描述: 包含付费套餐主表、产品表、定价表，支持灵活的套餐配置和定价策略
-- =====================================================



-- =====================================================
-- 1. 付费套餐主表 (hds_payment_packages)
-- 描述: 存储付费套餐的基本信息，支持按房间数量和按门店两种计费模式
-- =====================================================
create table hds_payment_packages
(
    id               int auto_increment comment '主键'
        primary key,
    package_code     varchar(32)                           not null comment '套餐编码，系统自动生成，格式：PKG_YYYYMMDD_XXXX',
    package_name     varchar(20)                           not null comment '套餐名称，唯一，最大20字符',
    payment_mode     enum('ROOM_COUNT','STORE_FIXED')      not null comment '付费方式：ROOM_COUNT-按房间数量收费，STORE_FIXED-按门店收费',
    discount_mode    enum('DISCOUNT','FIXED_PRICE')        not null comment '优惠方式：DISCOUNT-折扣，FIXED_PRICE-一口价',
    status           enum('ENABLED','DISABLED')            not null default 'DISABLED' comment '状态：ENABLED-启用，DISABLED-停用',
    is_recommended   tinyint                               not null default 0 comment '是否推荐套餐：0-否，1-是（仅支持一个推荐套餐）',
    sort_order       int                                   not null default 0 comment '排序权重，数字越大越靠前',
    description      text                                  null comment '套餐描述',
    created_by       varchar(64) default '1'               not null comment '创建人id',
    created_by_name  varchar(64) default '1'               not null comment '创建人名称',
    updated_by       varchar(64) default '1'               not null comment '修改人id',
    updated_by_name  varchar(64) default '1'               not null comment '修改人名称',
    created_at       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status       int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_package_code
        unique (package_code),
    constraint uk_package_name
        unique (package_name)
)
    comment '付费套餐主表';

create index idx_payment_mode
    on hds_payment_packages (payment_mode);

create index idx_status
    on hds_payment_packages (status);

create index idx_is_recommended
    on hds_payment_packages (is_recommended);

create index idx_sort_order
    on hds_payment_packages (sort_order);

-- =====================================================
-- 2. 套餐产品表 (hds_package_products)
-- 描述: 存储套餐包含的产品信息，每个套餐最多包含5个产品
-- =====================================================
create table hds_package_products
(
    id                      int auto_increment comment '主键'
        primary key,
    package_id              int                                   not null comment '套餐ID，外键关联hds_payment_packages.id',
    product_name            varchar(20)                           not null comment '产品名称，最大20字符',
    monthly_market_price    decimal(10,2)                         not null comment '月度门市价',
    monthly_discount_price  decimal(10,2)                         not null comment '月度优惠价',
    product_descriptions    json                                  null comment '产品描述JSON数组，最多10条，每条最大50字符',
    sort_order              int                                   not null default 0 comment '产品在套餐中的排序',
    status                  tinyint                               not null default 1 comment '状态：1-启用，0-停用',
    created_by              varchar(64) default '1'               not null comment '创建人id',
    created_by_name         varchar(64) default '1'               not null comment '创建人名称',
    updated_by              varchar(64) default '1'               not null comment '修改人id',
    updated_by_name         varchar(64) default '1'               not null comment '修改人名称',
    created_at              datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at              datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status              int                                   null comment '记录状态(1:有效, 0:无效)'
)
    comment '套餐产品表';

create index idx_package_id
    on hds_package_products (package_id);

create index idx_sort_order
    on hds_package_products (sort_order);

create index idx_status
    on hds_package_products (status);

-- =====================================================
-- 3. 套餐定价表 (hds_package_pricing)
-- 描述: 存储套餐产品的详细定价信息，支持月度/季度/年度定价
-- =====================================================
create table hds_package_pricing
(
    id               int auto_increment comment '主键'
        primary key,
    product_id       int                                   not null comment '产品ID，外键关联hds_package_products.id',
    period_type      enum('MONTHLY','QUARTERLY','YEARLY')  not null comment '周期类型：MONTHLY-月度，QUARTERLY-季度，YEARLY-年度',
    market_price     decimal(10,2)                         not null comment '门市价',
    discount_price   decimal(10,2)                         null comment '优惠价（一口价模式使用）',
    discount_rate    decimal(5,2)                          null comment '折扣比例，百分比（折扣模式使用，如10.50表示10.5%）',
    final_price      decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    is_custom_price  tinyint                               not null default 0 comment '是否自定义价格：0-使用默认计算，1-自定义设置',
    price_formula    varchar(255)                          null comment '价格计算公式说明',
    created_by       varchar(64) default '1'               not null comment '创建人id',
    created_by_name  varchar(64) default '1'               not null comment '创建人名称',
    updated_by       varchar(64) default '1'               not null comment '修改人id',
    updated_by_name  varchar(64) default '1'               not null comment '修改人名称',
    created_at       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status       int                                   null comment '记录状态(1:有效, 0:无效)',
    constraint uk_product_period
        unique (product_id, period_type)
)
    comment '套餐定价表';

create index idx_period_type
    on hds_package_pricing (period_type);

create index idx_final_price
    on hds_package_pricing (final_price);

-- =====================================================
-- 表结构创建完成
-- =====================================================
