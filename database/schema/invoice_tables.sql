-- =====================================================
-- 发票管理相关表结构设计
-- =====================================================

-- 1. 发票信息表（门店预设发票信息）
CREATE TABLE hds_invoice_info (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    hotel_code      VARCHAR(64) NOT NULL COMMENT '门店编码',
    invoice_title   VARCHAR(128) NOT NULL COMMENT '发票抬头',
    tax_number      VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email   VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content VARCHAR(64) DEFAULT '服务费' COMMENT '发票内容',
    more_info       TEXT COMMENT '更多信息/备注',
    created_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_hotel_code (hotel_code),
    KEY idx_tax_number (tax_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票信息表';

-- 2. 发票申请表
CREATE TABLE hds_invoice_application (
    id                    INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_no        VARCHAR(64) NOT NULL COMMENT '申请编号',
    hotel_code            VARCHAR(64) NOT NULL COMMENT '门店编码',
    hotel_name            VARCHAR(128) NOT NULL COMMENT '门店名称',
    invoice_header         VARCHAR(128) NOT NULL COMMENT '发票抬头',
    tax_number            VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email         VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content       VARCHAR(64) NOT NULL COMMENT '发票内容',
    more_info             TEXT COMMENT '更多信息/备注',
    invoice_amount        DECIMAL(10,2) NOT NULL COMMENT '发票金额',
    bill_count            INT NOT NULL COMMENT '关联账单数量',
    application_status    ENUM('PENDING','REVIEWING','APPROVED','REJECTED','INVOICING','INVOICED') 
                         DEFAULT 'PENDING' COMMENT '申请状态',
    review_result         VARCHAR(64) COMMENT '审核结果',
    review_remark         TEXT COMMENT '审核备注',
    reviewed_by           VARCHAR(64) COMMENT '审核人',
    reviewed_by_name      VARCHAR(64) COMMENT '审核人名称',
    reviewed_at           DATETIME COMMENT '审核时间',
    invoice_no            VARCHAR(64) COMMENT '发票号码',
    invoice_code          VARCHAR(32) COMMENT '发票代码',
    invoice_url           VARCHAR(512) COMMENT '发票文件URL',
    invoiced_at           DATETIME COMMENT '开票时间',
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_hotel_code (hotel_code),
    KEY idx_application_status (application_status),
    KEY idx_created_at (created_at),
    KEY idx_invoice_no (invoice_no)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票申请表';

-- 3. 发票明细表
CREATE TABLE hds_invoice_detail (
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_id    INT NOT NULL COMMENT '发票申请ID',
    bill_id           INT NOT NULL COMMENT '账单ID',
    bill_no           VARCHAR(64) NOT NULL COMMENT '账单编号',
    order_no          VARCHAR(64) NOT NULL COMMENT '订单编号',
    package_name      VARCHAR(64) NOT NULL COMMENT '套餐名称',
    service_period    VARCHAR(32) NOT NULL COMMENT '服务周期',
    service_start_time DATETIME NOT NULL COMMENT '服务开始时间',
    service_end_time  DATETIME NOT NULL COMMENT '服务结束时间',
    bill_amount       DECIMAL(10,2) NOT NULL COMMENT '账单金额',
    created_at        DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    row_status        INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    KEY idx_application_id (application_id),
    KEY idx_bill_id (bill_id),
    KEY idx_order_no (order_no),
    FOREIGN KEY fk_invoice_application (application_id) REFERENCES hds_invoice_application(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票明细表';

-- =====================================================
-- 发票表索引优化
-- =====================================================

-- 发票信息表索引
CREATE INDEX idx_invoice_info_hotel_code ON hds_invoice_info (hotel_code);
CREATE INDEX idx_invoice_info_tax_number ON hds_invoice_info (tax_number);

-- 发票申请表索引
CREATE INDEX idx_invoice_app_hotel_status ON hds_invoice_application (hotel_code, application_status);
CREATE INDEX idx_invoice_app_status_time ON hds_invoice_application (application_status, created_at);
CREATE INDEX idx_invoice_app_amount ON hds_invoice_application (invoice_amount);

-- 发票明细表索引
CREATE INDEX idx_invoice_detail_app_bill ON hds_invoice_detail (application_id, bill_id);
CREATE INDEX idx_invoice_detail_service_time ON hds_invoice_detail (service_start_time, service_end_time);

-- =====================================================
-- 发票表示例数据
-- =====================================================

-- 发票信息表示例数据
INSERT INTO hds_invoice_info (hotel_code, invoice_title, tax_number, receive_email, invoice_content, more_info) VALUES
('HOTEL_001', '北京某某酒店有限公司', '91110000000000000X', '<EMAIL>', '技术服务费', '请及时开具发票'),
('HOTEL_002', '上海某某大酒店', '91310000000000000Y', '<EMAIL>', '软件服务费', '月结发票'),
('HOTEL_003', '深圳某某酒店管理有限公司', '91440300000000000Z', '<EMAIL>', '信息技术服务费', '季度发票');

-- 发票申请表示例数据
INSERT INTO hds_invoice_application (
    application_no, hotel_code, hotel_name, invoice_title, tax_number, 
    receive_email, invoice_content, invoice_amount, bill_count, application_status
) VALUES
('INV_20240115_000001', 'HOTEL_001', '北京某某酒店', '北京某某酒店有限公司', '91110000000000000X', 
 '<EMAIL>', '技术服务费', 2400.00, 3, 'PENDING'),
('INV_20240115_000002', 'HOTEL_002', '上海某某大酒店', '上海某某大酒店', '91310000000000000Y', 
 '<EMAIL>', '软件服务费', 1800.00, 2, 'INVOICED'),
('INV_20240115_000003', 'HOTEL_003', '深圳某某酒店', '深圳某某酒店管理有限公司', '91440300000000000Z', 
 '<EMAIL>', '信息技术服务费', 7200.00, 1, 'REVIEWING');

-- 发票明细表示例数据
INSERT INTO hds_invoice_detail (
    application_id, bill_id, bill_no, order_no, package_name, 
    service_period, service_start_time, service_end_time, bill_amount
) VALUES
(1, 1, 'BILL_20240101_000001', 'ORD_20240101_000001', 'AI客服基础套餐', 
 'MONTHLY', '2024-01-01 00:00:00', '2024-01-31 23:59:59', 800.00),
(1, 2, 'BILL_20240201_000002', 'ORD_20240201_000002', 'AI客服基础套餐', 
 'MONTHLY', '2024-02-01 00:00:00', '2024-02-29 23:59:59', 800.00),
(1, 3, 'BILL_20240301_000003', 'ORD_20240301_000003', 'AI客服基础套餐', 
 'MONTHLY', '2024-03-01 00:00:00', '2024-03-31 23:59:59', 800.00);

-- =====================================================
-- 发票状态流转触发器（可选）
-- =====================================================

DELIMITER $$

CREATE TRIGGER tr_invoice_status_log 
AFTER UPDATE ON hds_invoice_application
FOR EACH ROW
BEGIN
    -- 当状态发生变化时，可以记录状态变更日志
    IF OLD.application_status != NEW.application_status THEN
        INSERT INTO hds_invoice_status_log (
            application_id, 
            old_status, 
            new_status, 
            changed_by, 
            changed_at
        ) VALUES (
            NEW.id, 
            OLD.application_status, 
            NEW.application_status, 
            NEW.updated_by, 
            NOW()
        );
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 发票相关视图（便于查询）
-- =====================================================

-- 发票申请详情视图
CREATE VIEW v_invoice_application_detail AS
SELECT 
    ia.id,
    ia.application_no,
    ia.hotel_code,
    ia.hotel_name,
    ia.invoice_title,
    ia.tax_number,
    ia.receive_email,
    ia.invoice_content,
    ia.invoice_amount,
    ia.bill_count,
    ia.application_status,
    ia.invoice_no,
    ia.invoice_code,
    ia.invoice_url,
    ia.created_at,
    ia.invoiced_at,
    GROUP_CONCAT(id.package_name) as package_names,
    GROUP_CONCAT(id.service_period) as service_periods
FROM hds_invoice_application ia
LEFT JOIN hds_invoice_detail id ON ia.id = id.application_id
WHERE ia.row_status = 1
GROUP BY ia.id;

-- 门店发票统计视图
CREATE VIEW v_hotel_invoice_statistics AS
SELECT 
    hotel_code,
    hotel_name,
    COUNT(*) as total_applications,
    SUM(CASE WHEN application_status = 'INVOICED' THEN 1 ELSE 0 END) as invoiced_count,
    SUM(CASE WHEN application_status = 'PENDING' THEN 1 ELSE 0 END) as pending_count,
    SUM(CASE WHEN application_status = 'INVOICED' THEN invoice_amount ELSE 0 END) as total_invoiced_amount,
    MAX(created_at) as last_application_time
FROM hds_invoice_application
WHERE row_status = 1
GROUP BY hotel_code, hotel_name;
