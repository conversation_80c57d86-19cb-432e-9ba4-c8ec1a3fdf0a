1. Ai运营后台-付费管理
   模块
   核心功能
   功能描述
   参考图（具体请以功能描述为准）
   付费管理
   付费管理
   新增付费套餐
1. 自定义套餐名称：
1. 名称为唯一值，不支持重复命名（名称不超过20个字符）
2. 每组套餐必须先设置【付费方式】和【优惠方式】，在同一个【付费方式】和【优惠方式】的前提下新增产品，每组套餐最多不超过5个产品
2. 选择付费方式：（1）按房间数量收费；（2）按门店收费（单选）
1. 付费方式说明：
   1. 【按房间数量收费】：系统将自动读取酒店房间数量，最终费用 = 房间单价 × 房间数量
   2. 【按门店收费】：系统将自动固定统一费用，不考虑房间数量，每个酒店收取固定金额
3. 支持选择优惠方式：折扣、一口价（单选）
1. 支持价格独立设置一口价（低于门市价）
2. 支持价格独立设置折扣比例（基于月度优惠价打折）
4. 组合产品套餐
1. 自定义产品名称：（名称不超过20个字符）
2. 产品价格设置：
   1. 必须设置月度基础价格：包含门市价、优惠价（立省金额=门店价-优惠价）
   2. （选择一口价展示）价格计算：月度价格为基础价，默认季度价格 = 月度价格 × 3 ，年度价格 = 月度价格 × 12；支持季度和年度价格设置一口价
    1. 一口价展示保留2位小数
       3. （选择折扣展示）价格计算：月底价格为基础价，默认季度价格 = 月度价格 × 3 ，年度价格 = 月度价格 × 12；支持季度和年度价格设置折扣比例
    1. 折扣后的金额四舍五入保留2位小数
3. 产品描述（支持添加产品描述展示，展示产品描述不超过10个，每个产品描述字符不超过50个字符）
   1. （1）产品功能一
   2. （2）产品功能二
5. 支持增加产品套餐
1. 支持组合产品套餐新增
6. 按钮：取消｜保存
1. 保存：支持重新编辑后保存
   1. 如果二次保存，则提示“更新将覆盖原有活动优惠内容，请确认是否保存”
    1. 按钮：取消｜确认

[图片]


1. 计算逻辑：
1. 一口价模式：
   - 显示"立省金额"列
   - 实时计算：门市价 - 优惠价 = 立省金额
   - 支持季度和年度单独设置一口价金额
2. 折扣模式：
   1. 显示"折扣力度"列
   2. 以"立省X%"的形式展示折扣程度
   3. 支持季度和年度单独设置折扣比例
2. 【一口价】计算规则
- 月度优惠价：继续使用设置的月度优惠价
- 季度优惠价：
    - 如果设置了季度一口价 → 直接使用设置的一口价
    - 如果未设置 → 默认为月度优惠价 × 3
- 年度优惠价：
    - 如果设置了年度一口价 → 直接使用设置的一口价
    - 如果未设置 → 默认为月度优惠价 × 12
- 计算示例
    2. 【情况一：不设置】价格计算公式
    - 月度优惠价：20元
    - 季度优惠价：100 × 3 = 60元
    - 年度优惠价：100 × 12 = 240元
      [图片]
    3. 【情况二：设置一口价】价格计算公式
    - 月度优惠价：20元：
    - 季度一口价设置：50元 → 季度优惠价 = 50元（而不是60元）
    - 年度一口价设置：120元 → 年度优惠价 = 120元（而不是240元）
      [图片]
3. 【折扣】价格计算公式
- 月度优惠价=设置的月度优惠价
- 季度优惠价 = 月度优惠价 × 3 × (1 - 折扣比例)
- 年度优惠价 = 月度优惠价 × 12 × (1 - 折扣比例)
  折扣力度显示规则
- 月度折扣力度：显示为 "-"
- 季度折扣力度：显示设置的折扣比例，如 "立省10.0%"
- 年度折扣力度：显示设置的折扣比例，如 "立省20.0%"
  计算示例
  假设：
- 月度门市价：1000元，月度优惠价：800元
- 季度折扣比例：15%
- 年度折扣比例：25%
  最终价格展示：
  [图片]
  计算过程：
- 季度优惠价 = 800 × 3 × (1 - 0.15) = 800 × 3 × 0.85 = 2040元
- 年度优惠价 = 800 × 12 × (1 - 0.25) = 800 × 12 × 0.75 = 7200元
  1.产品模式设置-一口价
  [图片]
  2.产品模式设置-折扣价
  [图片]


付费套餐列表

1. 搜索导航
1. 搜索栏导航（支持多个条件筛选下搜索）
   1. 套餐名称搜索
    1. 支持关键词模糊-索引全程
       2. 付费模式筛选
       3. 优惠方式筛选
2. 搜索栏左下方按钮：查询、新增付费套餐
2. 付费套餐列表：
1. 套餐编码和创建时间（系统新增后自动生成）
2. 列表字段：套餐编码、套餐名称、付费模式、优惠方式、表格展示（产品名称、月度门市价/优惠价、季度门市价/优惠价、年度门市价/优惠价---“多个产品展示多条表格记录”）、创建时间
3. 操作：编辑｜启用&停用｜设为推荐&取消推荐
   1. 启用&停用
    1. 新增保存后默认为启用状态
    2. 启用=付费套餐启用状态
    3. 停用=付费套餐停止使用状态
    1. 当用户进入付费套餐付费过程中，套餐操作停用，则该套餐无法支付，页面提示：当前套餐已下架，请更换套餐购买
    4. 操作需二次确认
    1. 操作提示文案：确认启动/禁用套餐
    2. 按钮：确认/取消
       2. 设置/取消推荐套餐
    1. 未操作【设为推荐】，支持操作【设为推荐】，【推荐】默认列表置顶
    2. 仅支持设置一个【设为推荐】，设置后，则该套餐为所有门店的默认付费套餐
    3. 操作提示文案：确认设为推荐/取消推荐
    4. 按钮：确认/取消

一、套餐列表
产品信息参考样式一：
[图片]
产品信息参考样式二：（看产品表即可）：
[图片]

二、操作二次确认提示栏：
[图片]
取消推荐
[图片]
设为推荐
[图片]
停用套餐
[图片]
启用套餐

门店付费套餐列表
门店付费设置

1. 点击【新增门店套餐】
1. 进入【门店付费设置】
2. 选择门店
   1. 支持模糊输入和筛选
3. 选择【付费套餐】
4. 同步门店信息：
   1. 门店名称、房间数量、计费方式、优惠方式
    1. 门店名称：默认具备【OTA智能体】权限的门店可支持筛选设置
    2. 通过【付费套餐】关联：计费方式、优惠方式，不支持更改
    3. 计费方式：
    1. 选择按房间计费，则门店费用=门市价/优惠价✖️房间数量
    2. 选择按酒店计费，则门店费用=门市价/优惠价
    4. 优惠方式：
    1. 一口价：支持门店产品按月、季、年单独设置一口价
    2. 折扣：支持门店产品按月、季、年单独设置折扣金额
       2. 门店个性化价格设置-同步门店选择的【付费套餐】进行设置
    1. 留空则使用默认价格，填写则使用个性化价格，季度、年度价格支持独立设置；
    2. 不做价格高低校验，设置多少，则该门店展示多少金额
       3. 展示最终价格：
    1. 按最终设置的价格进行表格展示
       4. 按钮：
    1. 取消｜保存
    1. 点击【取消】
       1. 则不需保存编辑记录，取消回退到【门店费用列表】
    2. 点击【保存】
       1. 保存后跳转到【门店费用列表】
       2. 保存后默认为【停用】状态，需要在【门店费用列表】内进行启用，前端方可覆盖默认价格，展示为最新按门店设置的套餐价格展示
2. 编辑入口：
1. 不支持更改关联的【门店名称】
2. 不支持更改关联的【付费套餐】，以及计费方式、优惠方式
3. 仅支持自定义价格更改
4. 通过【门店费用列表】的【编辑】入口进入
   1. 编辑后需点击【保存】，前端方可覆盖默认价格，展示为最新按门店设置的套餐价格展示
   [图片]
   新增入口
   [图片]
   编辑入口


门店付费套餐列表

1. 搜索导航
1. 搜索栏导航（支持多个条件筛选下搜索）
   1. 门店筛选
    1. 支持关键词模糊-索引全称
       2. 套餐名称筛选
    1. 支持关键词模糊-索引全称
       3. 状态筛选
    1. 支持门店付费套餐状态“启用/禁用”
2. 搜索栏左下方按钮：查询｜新增门店套餐
2. 门店付费套餐列表：
1. 门店套餐编码及创建时间（新增后系统自动生成）
2. 字段：门店套餐编码、门店名称、套餐名称、状态、付费模式、优惠方式、产品信息、创建时间
   1. 产品信息=门店自定义保存后的价格
   2. 价格更改需保存日志，日志放置日志管理留存
3. 操作：编辑｜停用&启用&删除
   1. 递进流程“启用-停用-删除”
    1. 从【停用】-【启用】/【启用】-【停用】，可以直接设置
    2. 【启用】状态，需更新为【停用】状态，方可进行【删除】
    3. 【删除】仅需前端隐藏，无需【删除】数据
       2. 需二次确认：操作提示文案：确认启动/禁用/删除门店付费套餐
    1. 按钮：确认/取消
       [图片]
2. Ai门店后台-支付入口
   模块
   核心功能
   功能描述
   参考图（具体请以功能描述为准）
   支付入口
-

1. 支付入口展示逻辑：
1. 邀请码逻辑更改：原本送1个月有效期，改成7天有效期（7天待定）
2. 邀请码过期后/7天后支持展示
2. 选择套餐内容后，则选择按钮和框“亮色”展示
3. 点击购买，则调用对应价格的支付页面




1. 页面内容展示：
1. 标题：订阅OTA智能体，解锁更多能力
2. 副标题：选择合适你的套餐
3. 三个时间周期选择：月、季（节省XX%/节省¥XX）、年（节省XX%/节省¥XX）
4. 产品组合套餐展示
   1. 价格从低到高进行依次展示，根据门店所属付费套餐内容进行展示；
    1. 如是默认的推荐付费套餐，则按默认套餐进行展示；
    2. 如是设置了自定义的门店付费套餐，则按门店付费套餐进行展示；
       2. 三个产品： （月度示例）
    1. 产品名称1：¥120/月（原价¥500/月），单月仅需¥120
    2. 产品名称2：¥300/月（原价¥900/月），单月仅需¥300
    3. 产品名称3：¥500/月（原价¥1200/月），单月仅需¥500
       3. 三个产品： （季度示例）*3*（1-折扣比例）｜优惠价*3｜一口价
    1. 产品名称1：¥360/季（原价¥1500/季），单月仅需¥120
    2. 产品名称2：¥900/季（原价¥2700/季），单月仅需¥300
    3. 产品名称3：¥1500/季（原价¥3600/季），单月仅需¥500
       4. 三个产品： （年度示例）*12*（1-折扣比例）｜优惠价*12｜一口价
    1. 产品名称1：¥1440/年（原价¥6000/年），单月仅需¥120
    2. 产品名称2：¥3600/年（原价¥10800/季），单月仅需¥300
    3. 产品名称3：¥6000/季（原价¥14400/季），单月仅需¥500
5. 产品描述:（对应不同产品展示对应产品能力
   1. 产品能力一
   2. 产品能力二
   [图片]



1. 交易页面
1. 调用支付界面，支持微信/支付宝扫码支付
2. 支付默认同意《付费套餐服务协议》
   1. 文案：我已阅读并同意《付费套餐服务协议》，虚拟商品一经支付不支持退款

[图片]




3. Ai门店后台-结算管理
   模块
   核心功能
   功能描述
   参考图（具体请以功能描述为准）
   结算管理

交易列表
1. 搜索框
2. 支持订单编号搜索
   1. 精确搜索
3. 支持套餐名称搜索
   1. 模糊搜索
4. 点击按钮：查询
2. 交易订单列表：字段信息如下：
1. 订单编号 - 唯一标识，系统自动生成
2. 套餐信息 - 付费套餐名称
3. 购买产品名称 - 套餐内购买的具体产品名称
4. 购买周期 - 月度/季度/年度
   10. 购买周期显示
   11. 月度：绿色显示
   12. 季度：黄色显示
   13. 年度：红色显示
5. 订单金额 - 实际支付金额
6. 订单状态 - 待支付/已支付/已取消
   1. 订单支付扭转
7. 创建时间 - 订单创建时间
8. 产品到期时间 - 服务到期日期
   1. 显示产品的具体到期日期
   2. 未支付和已取消订单显示"-"
9. 支付方式 - 微信/支付宝（本期支持微信和支付宝支付即可）
3. 订单记录
1. 按最新时间进行排序
2. 10条记录后，支持翻页查看
3. 若无数据，则仅展示表头即可
   交易订单列表：
   [图片]
   [图片]

账单列表
1. 搜索框
2. 支持流水号搜索
   1. 精确搜索
3. 支持订单编号搜索
   1. 精确搜索
4. 支持套餐名称搜索
   1. 模糊搜索
5. 点击按钮：查询｜开发票
2. 账单列表：字段信息如下：
1. 流水号 - 支付流水标识，从易宝获取
2. 订单编号 - 原始订单编号，系统自动生成
3. 套餐信息 - 套餐名称
4. 购买产品名称 - 具体付费套餐关联支付的产品名称
5. 购买周期 - 月度/季度/年度
6. 到期时间 - 产品服务到期日期
7. 支付金额 - 实际支付金额
8. 支付方式 - 微信/支付宝
9. 交易状态 - 交易成功/已退款（本期不做已退款逻辑）
10. 订单创建时间 - 下单时间
11. 订单交易时间 - 支付完成时间
12. 操作 - 点击【开发票】/【申请发票】按钮
    1. 支持勾选多个账单
1. 勾选需要开票的账单记录
2. 系统实时统计选中数量和金额
3. 退款记录自动排除，仅支持【交易成功】状态订单【申请开票】
   第二步：跳转到【开具发票】页面
3. 订单记录
1. 按最新时间进行排序
2. 10条记录后，支持翻页查看
3. 若无数据，则仅展示表头即可
   [图片]


开票管理
1. 发票信息设置
1. 首次使用：需要填写完整发票信息
2. 再次使用：自动填充已保存的信息，支持编辑
2. 表单字段
* 发票抬头 ⭐ 必填：企业名称，支持保存后重新编辑
* 纳税人识别号 ⭐ 必填：纳税人识别号，支持保存后重新编辑
* 发票内容：默认"服务费"（只读）
* 发票金额：自动计算选中账单总额（只读）
* 接收邮箱 ⭐ 必填，填写邮箱格式，支持保存后重新编辑
* 更多信息：非必填的备注信息，支持保存后重新编辑
  选中账单展示
* 显示所有选中的账单详情
* 包含：流水号、套餐名称、金额
* 底部显示发票总金额
3. 按钮操作：
1. 点击【返回账单】则不保存发票信息，返回到账单列表页面
2. 点击【保存发票信息】则保存已填写的发票信息（包含发票抬头、税号、邮箱、以及更多信息）
3. 点击【提交电子发票】，即生成开票记录
   1. 开票记录按填写的发票信息和关联的账单进行生成记录；

[图片]
[图片]


开票列表展示
1. 搜索栏
1. 支持申请编号搜索
   1. 精确搜索
2. 支持申请状态筛选
   1. 状态包含：待审核｜审核中｜已开票｜已驳回
3. 点击按钮：查询
2. 开票记录列表
1. 申请编号：系统自动生成
2. 发票信息：获取发票抬头和税号
3. 关联账单：统计数量（不需展示编号）
4. 发票金额：根据记录统计金额
5. 申请状态
   1. 待审核：提交后的初始状态，可修改/取消
   2. 审核中：通过初审，正在开票流程中
   3. 已开票：完成开具，可下载发票
   4. 已驳回：审核未通过，可重新申请
6. 申请时间：发起申请的时候
7. 开票时间：运营后台点击状态为【已开票】的时间
8. 预计处理：（本期不需要展示）
9. 操作：
   1. 【待审核】状态下支持该开票记录
    1. 【取消申请】则申请记录删除（前端删除）
    2. 【修改申请】支持修改并更新开票信息
    1. 进入开票信息编辑页面，修改后，点击【提交电子发票】即可更新开票信息；
       2. 【下载发票】本期不做逻辑，不展示该功能
       3. 【查看详情】即可查看开票信息页面，不支持更改信息
3. 订单记录
1. 按最新时间进行排序
2. 10条记录后，支持翻页查看
3. 若无数据，则仅展示表头即可
   [图片]

4. Ai运营后台-结算管理
   模块
   核心功能
   功能描述
   参考图（具体请以功能描述为准）
   结算管理

交易
1. 搜索导航栏
1. 筛选门店/门店编码
   1. 支持关键词模糊-索引全称/支持模糊搜索
2. 支持按订单状态
3.
2. 账单列表
1. 总账单；
   1. 账单字段：周期/支付类型/交易笔数/支付金额/支付
2. 交易记录：充值、消费、退款明细（时间、金额、操作人）。
3. 账单生成：按月生成对账单，支持PDF/Excel导出。


账单
1. 搜索导航栏
1. 筛选门店/门店编码
   1. 支持关键词模糊-索引全称/支持模糊搜索
2. 支持按订单状态
3.
2. 账单列表
1. 总账单；
   1. 账单字段：周期/支付类型/交易笔数/支付金额/支付
2. 交易记录：充值、消费、退款明细（时间、金额、操作人）。
3. 账单生成：按月生成对账单，支持PDF/Excel导出。


开票
1. 开票申请：
1. 商户提交申请（选择充值记录或消费周期）。
2. 发票类型（普票/专票/电子票）
2. 历史记录：
1. 发票状态跟踪（待审核/已开票/驳回），驳回需填写原因。

