# AI运营后台付费管理系统 - 数据库设计文档

## 概述

本文档描述了付费管理系统的数据库设计方案，包括表结构设计、索引优化、分库分表策略等。

## 数据库架构

### 1. 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                        应用层                                │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (MyBatis-Plus)                 │
├─────────────────────────────────────────────────────────────┤
│  主库 (Master)     │  从库1 (Slave1)  │  从库2 (Slave2)     │
│  写操作            │  读操作          │  读操作             │
├─────────────────────────────────────────────────────────────┤
│                    MySQL 8.0 集群                           │
└─────────────────────────────────────────────────────────────┘
```

### 2. 读写分离配置
```yaml
spring:
  datasource:
    master:
      url: ******************************************
      username: root
      password: password
    slave:
      - url: ******************************************
        username: readonly
        password: password
      - url: ******************************************
        username: readonly
        password: password
```

## 核心表设计

### 1. 套餐管理表组

#### 1.1 付费套餐表 (hds_payment_package)
```sql
CREATE TABLE hds_payment_package (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    package_code    VARCHAR(64) NOT NULL COMMENT '套餐编码',
    package_name    VARCHAR(64) NOT NULL COMMENT '套餐名称',
    payment_mode    TINYINT NOT NULL COMMENT '付费方式：0-按房间数量，1-按门店',
    discount_mode   TINYINT NOT NULL COMMENT '优惠方式：1-折扣，2-一口价',
    status          TINYINT DEFAULT 1 NOT NULL COMMENT '状态：1-启用，0-停用',
    is_recommended  TINYINT DEFAULT 0 NOT NULL COMMENT '是否推荐：1-是，0-否',
    created_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_package_code (package_code),
    UNIQUE KEY uk_package_name (package_name),
    KEY idx_payment_mode (payment_mode),
    KEY idx_status (status),
    KEY idx_is_recommended (is_recommended)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付费套餐表';
```

**设计要点**：
- 使用自增主键，提升插入性能
- 套餐编码和名称唯一约束
- 状态字段支持软删除
- 推荐套餐标识，业务层保证唯一性

#### 1.2 付费产品表 (hds_payment_product)
```sql
CREATE TABLE hds_payment_product (
    id                  INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    package_id          INT NOT NULL COMMENT '套餐ID',
    product_name        VARCHAR(64) NOT NULL COMMENT '产品名称',
    product_description JSON COMMENT '产品描述JSON数组',
    sort_order          INT DEFAULT 0 NOT NULL COMMENT '排序',
    created_by          VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name     VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by          VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name     VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at          DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at          DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status          INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    KEY idx_package_id (package_id),
    KEY idx_sort_order (sort_order),
    FOREIGN KEY fk_product_package (package_id) REFERENCES hds_payment_package(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='付费产品表';
```

**设计要点**：
- 外键关联套餐表，支持级联删除
- JSON字段存储产品描述，灵活扩展
- 排序字段支持产品排序显示

#### 1.3 产品定价表 (hds_product_pricing)
```sql
CREATE TABLE hds_product_pricing (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    product_id      INT NOT NULL COMMENT '产品ID',
    period_type     ENUM('MONTHLY','QUARTERLY','YEARLY') NOT NULL COMMENT '周期类型',
    market_price    DECIMAL(10,2) NOT NULL COMMENT '门市价',
    discount_price  DECIMAL(10,2) COMMENT '优惠价',
    discount_rate   DECIMAL(5,2) COMMENT '折扣比例',
    final_price     DECIMAL(10,2) NOT NULL COMMENT '最终价格',
    created_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_product_period (product_id, period_type),
    KEY idx_period_type (period_type),
    KEY idx_final_price (final_price),
    FOREIGN KEY fk_pricing_product (product_id) REFERENCES hds_payment_product(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品定价表';
```

**设计要点**：
- 规范化设计，按周期类型分离存储
- 复合唯一约束防止重复定价
- 使用DECIMAL类型保证价格精度

### 2. 订单管理表组

#### 2.1 交易订单表 (hds_payment_order)
```sql
CREATE TABLE hds_payment_order (
    id                    INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    order_no              VARCHAR(64) NOT NULL COMMENT '订单编号',
    hotel_code            VARCHAR(64) NOT NULL COMMENT '酒店编码',
    hotel_package_id      INT NOT NULL COMMENT '门店套餐ID',
    purchase_period       ENUM('MONTHLY','QUARTERLY','YEARLY') NOT NULL COMMENT '购买周期',
    room_count            INT COMMENT '房间数量',
    original_amount       DECIMAL(10,2) NOT NULL COMMENT '原价金额',
    discount_amount       DECIMAL(10,2) DEFAULT 0.00 NOT NULL COMMENT '优惠金额',
    order_amount          DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    service_start_time    DATETIME COMMENT '服务开始时间',
    service_end_time      DATETIME COMMENT '服务结束时间',
    order_status          ENUM('PENDING','PAID','CANCELLED','EXPIRED') DEFAULT 'PENDING' COMMENT '订单状态',
    payment_method        ENUM('WECHAT','ALIPAY') COMMENT '支付方式',
    paid_at               DATETIME COMMENT '支付时间',
    expire_at             DATETIME COMMENT '订单过期时间',
    remark                TEXT COMMENT '备注信息',
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_order_no (order_no),
    KEY idx_hotel_code (hotel_code),
    KEY idx_hotel_package_id (hotel_package_id),
    KEY idx_order_status (order_status),
    KEY idx_created_at (created_at),
    KEY idx_service_time (service_start_time, service_end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易订单表';
```

**分表策略**：
```sql
-- 按酒店编码分表，支持水平扩展
CREATE TABLE hds_payment_order_0 LIKE hds_payment_order;
CREATE TABLE hds_payment_order_1 LIKE hds_payment_order;
-- ... 更多分表
```

#### 2.2 订单明细表 (hds_order_detail)
```sql
CREATE TABLE hds_order_detail (
    id           INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    order_id     INT NOT NULL COMMENT '订单ID',
    product_id   INT NOT NULL COMMENT '产品ID',
    product_name VARCHAR(64) NOT NULL COMMENT '产品名称',
    period_type  ENUM('MONTHLY','QUARTERLY','YEARLY') NOT NULL COMMENT '周期类型',
    unit_price   DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity     INT DEFAULT 1 NOT NULL COMMENT '数量',
    amount       DECIMAL(10,2) NOT NULL COMMENT '小计金额',
    created_at   DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    row_status   INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    KEY idx_order_id (order_id),
    KEY idx_product_id (product_id),
    FOREIGN KEY fk_detail_order (order_id) REFERENCES hds_payment_order(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';
```

### 3. 支付账单表组

#### 3.1 账单表 (hds_payment_bill)
```sql
CREATE TABLE hds_payment_bill (
    id                 INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    bill_no            VARCHAR(64) NOT NULL COMMENT '账单编号',
    transaction_no     VARCHAR(64) NOT NULL COMMENT '流水号',
    order_id           INT NOT NULL COMMENT '订单ID',
    hotel_code         VARCHAR(64) NOT NULL COMMENT '酒店编码',
    payment_amount     DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    payment_method     ENUM('WECHAT','ALIPAY') NOT NULL COMMENT '支付方式',
    transaction_status ENUM('SUCCESS','REFUNDED','FAILED') DEFAULT 'SUCCESS' COMMENT '交易状态',
    transaction_at     DATETIME NOT NULL COMMENT '交易完成时间',
    refund_amount      DECIMAL(10,2) COMMENT '退款金额',
    refund_at          DATETIME COMMENT '退款时间',
    refund_reason      VARCHAR(255) COMMENT '退款原因',
    invoice_status     ENUM('NONE','APPLIED','INVOICED') DEFAULT 'NONE' COMMENT '开票状态',
    created_by         VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name    VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by         VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name    VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at         DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at         DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status         INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_transaction_no (transaction_no),
    UNIQUE KEY uk_bill_no (bill_no),
    KEY idx_order_id (order_id),
    KEY idx_hotel_code (hotel_code),
    KEY idx_transaction_at (transaction_at),
    KEY idx_invoice_status (invoice_status),
    FOREIGN KEY fk_bill_order (order_id) REFERENCES hds_payment_order(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账单表';
```

## 索引优化策略

### 1. 主键索引
- 所有表使用自增主键，聚簇索引
- 提升范围查询和排序性能

### 2. 唯一索引
```sql
-- 业务唯一性约束
ALTER TABLE hds_payment_package ADD UNIQUE KEY uk_package_code (package_code);
ALTER TABLE hds_payment_order ADD UNIQUE KEY uk_order_no (order_no);
ALTER TABLE hds_payment_bill ADD UNIQUE KEY uk_transaction_no (transaction_no);
```

### 3. 复合索引
```sql
-- 订单查询优化
ALTER TABLE hds_payment_order ADD KEY idx_hotel_status_time (hotel_code, order_status, created_at);

-- 账单查询优化
ALTER TABLE hds_payment_bill ADD KEY idx_hotel_invoice_time (hotel_code, invoice_status, transaction_at);

-- 定价查询优化
ALTER TABLE hds_product_pricing ADD KEY idx_product_period_price (product_id, period_type, final_price);
```

### 4. 覆盖索引
```sql
-- 订单列表查询覆盖索引
ALTER TABLE hds_payment_order ADD KEY idx_cover_order_list (
    hotel_code, order_status, created_at, order_no, order_amount, purchase_period
);
```

## 分库分表策略

### 1. 垂直分库
```
payment_package_db: 套餐管理相关表
payment_order_db:   订单管理相关表
payment_bill_db:    账单管理相关表
payment_invoice_db: 发票管理相关表
```

### 2. 水平分表
```sql
-- 订单表按酒店编码分表
CREATE TABLE hds_payment_order_0 LIKE hds_payment_order;
CREATE TABLE hds_payment_order_1 LIKE hds_payment_order;
-- 分表规则：CRC32(hotel_code) % 16

-- 账单表按时间分表
CREATE TABLE hds_payment_bill_202501 LIKE hds_payment_bill;
CREATE TABLE hds_payment_bill_202502 LIKE hds_payment_bill;
-- 分表规则：按月分表
```

### 3. 分表路由配置
```java
@Component
public class ShardingTableRule {
    
    public String getOrderTableName(String hotelCode) {
        int hash = Math.abs(hotelCode.hashCode());
        int tableIndex = hash % 16;
        return "hds_payment_order_" + tableIndex;
    }
    
    public String getBillTableName(Date transactionDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
        return "hds_payment_bill_" + sdf.format(transactionDate);
    }
}
```

## 数据归档策略

### 1. 冷热数据分离
```sql
-- 历史订单归档表
CREATE TABLE hds_payment_order_archive LIKE hds_payment_order;

-- 归档策略：6个月前的已完成订单
INSERT INTO hds_payment_order_archive 
SELECT * FROM hds_payment_order 
WHERE order_status IN ('PAID', 'CANCELLED') 
  AND created_at < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

### 2. 定期清理
```sql
-- 清理过期的未支付订单
DELETE FROM hds_payment_order 
WHERE order_status = 'PENDING' 
  AND expire_at < NOW() 
  AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
```

## 备份恢复策略

### 1. 全量备份
```bash
# 每日全量备份
mysqldump --single-transaction --routines --triggers \
  --master-data=2 payment_system > backup_$(date +%Y%m%d).sql
```

### 2. 增量备份
```bash
# 开启binlog
log-bin = mysql-bin
binlog-format = ROW
expire_logs_days = 7

# 增量备份脚本
mysqlbinlog --start-datetime="2025-01-10 00:00:00" \
  --stop-datetime="2025-01-10 23:59:59" mysql-bin.000001 > incremental.sql
```

### 3. 主从同步
```sql
-- 主库配置
server-id = 1
log-bin = mysql-bin
binlog-do-db = payment_system

-- 从库配置
server-id = 2
relay-log = mysql-relay-bin
read-only = 1
```

## 性能监控

### 1. 慢查询监控
```sql
-- 开启慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
```

### 2. 性能指标监控
```sql
-- 关键性能指标
SHOW GLOBAL STATUS LIKE 'Threads_connected';
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_read_requests';
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_reads';
SHOW GLOBAL STATUS LIKE 'Com_select';
SHOW GLOBAL STATUS LIKE 'Com_insert';
SHOW GLOBAL STATUS LIKE 'Com_update';
SHOW GLOBAL STATUS LIKE 'Com_delete';
```

这个数据库设计方案提供了完整的表结构、索引优化、分库分表和运维策略，支持高并发、高可用的业务需求。
