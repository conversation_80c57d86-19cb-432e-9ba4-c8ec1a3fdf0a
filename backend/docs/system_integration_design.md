# 付费管理系统 - 系统集成设计

## 1. 集成架构概述

### 1.1 系统集成全景图

```mermaid
graph TB
    subgraph "外部系统"
        A[微信支付]
        B[支付宝]
        C[发票开票系统]
        D[短信服务]
        E[邮件服务]
    end
    
    subgraph "付费管理系统"
        F[API Gateway]
        G[套餐管理服务]
        H[订单管理服务]
        I[支付管理服务]
        J[发票管理服务]
        K[通知服务]
    end
    
    subgraph "内部系统"
        L[用户权限系统]
        M[门店管理系统]
        N[设备管理系统]
        O[OTA智能体系统]
    end
    
    subgraph "基础设施"
        P[MySQL数据库]
        Q[Redis缓存]
        R[RocketMQ消息队列]
        S[Nacos注册中心]
    end
    
    A --> I
    B --> I
    C --> J
    D --> K
    E --> K
    
    F --> G
    F --> H
    F --> I
    F --> J
    
    G --> L
    H --> M
    I --> N
    J --> O
    
    G --> P
    H --> P
    I --> P
    J --> P
    
    G --> Q
    H --> Q
    I --> Q
    J --> Q
    
    H --> R
    I --> R
    J --> R
    K --> R
    
    F --> S
    G --> S
    H --> S
    I --> S
    J --> S
```

### 1.2 集成方式说明

| 集成类型 | 集成方式 | 协议 | 说明 |
|----------|----------|------|------|
| 支付集成 | REST API | HTTPS | 微信支付、支付宝支付接口 |
| 发票集成 | REST API | HTTPS | 第三方发票开票系统 |
| 通知集成 | REST API | HTTPS | 短信、邮件服务接口 |
| 内部服务 | RPC调用 | HTTP/gRPC | 微服务间调用 |
| 消息通信 | 消息队列 | TCP | 异步消息处理 |

## 2. 支付系统集成

### 2.1 支付集成架构

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant G as 网关
    participant P as 支付服务
    participant W as 微信支付
    participant A as 支付宝
    participant O as 订单服务
    
    U->>F: 选择支付方式
    F->>G: 发起支付请求
    G->>P: 创建支付订单
    
    alt 微信支付
        P->>W: 调用微信支付API
        W-->>P: 返回支付参数
    else 支付宝支付
        P->>A: 调用支付宝API
        A-->>P: 返回支付参数
    end
    
    P-->>G: 返回支付信息
    G-->>F: 返回支付参数
    F-->>U: 展示支付页面
    
    U->>W: 完成支付
    W->>P: 支付回调通知
    P->>O: 更新订单状态
    P-->>W: 返回处理结果
```

### 2.2 支付接口适配器设计

```java
/**
 * 支付接口适配器
 */
public interface PaymentAdapter {
    
    /**
     * 创建支付订单
     */
    PaymentCreateResult createPayment(PaymentCreateRequest request);
    
    /**
     * 查询支付状态
     */
    PaymentQueryResult queryPayment(String outTradeNo);
    
    /**
     * 处理支付回调
     */
    PaymentCallbackResult handleCallback(HttpServletRequest request);
    
    /**
     * 申请退款
     */
    RefundResult refund(RefundRequest request);
}

/**
 * 微信支付适配器
 */
@Component
public class WechatPaymentAdapter implements PaymentAdapter {
    
    @Override
    public PaymentCreateResult createPayment(PaymentCreateRequest request) {
        // 1. 构建微信支付参数
        WxPayUnifiedOrderRequest wxRequest = new WxPayUnifiedOrderRequest();
        wxRequest.setOutTradeNo(request.getOrderNo());
        wxRequest.setTotalFee(request.getAmount().multiply(new BigDecimal("100")).intValue());
        wxRequest.setBody(request.getSubject());
        wxRequest.setNotifyUrl(request.getNotifyUrl());
        
        // 2. 调用微信支付API
        WxPayUnifiedOrderResult wxResult = wxPayService.unifiedOrder(wxRequest);
        
        // 3. 构建返回结果
        return PaymentCreateResult.builder()
            .paymentId(wxResult.getPrepayId())
            .paymentUrl(wxResult.getCodeUrl())
            .qrCode(generateQrCode(wxResult.getCodeUrl()))
            .expireTime(LocalDateTime.now().plusMinutes(15))
            .build();
    }
    
    @Override
    public PaymentCallbackResult handleCallback(HttpServletRequest request) {
        // 1. 验证回调签名
        String xmlData = getRequestBody(request);
        WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(xmlData);
        
        // 2. 验证支付结果
        if ("SUCCESS".equals(notifyResult.getResultCode())) {
            return PaymentCallbackResult.builder()
                .success(true)
                .outTradeNo(notifyResult.getOutTradeNo())
                .transactionId(notifyResult.getTransactionId())
                .totalFee(new BigDecimal(notifyResult.getTotalFee()).divide(new BigDecimal("100")))
                .build();
        }
        
        return PaymentCallbackResult.builder().success(false).build();
    }
}
```

### 2.3 支付状态同步机制

```mermaid
flowchart TD
    A[支付回调] --> B[验证签名]
    B --> C{签名验证}
    C -->|失败| D[记录异常日志]
    C -->|成功| E[解析回调数据]
    E --> F[查询本地订单]
    F --> G{订单状态}
    G -->|已支付| H[返回成功响应]
    G -->|待支付| I[更新订单状态]
    I --> J[创建账单记录]
    J --> K[发送支付成功消息]
    K --> L[更新门店服务状态]
    L --> M[发送用户通知]
    M --> N[返回成功响应]
    
    D --> O[支付状态查询补偿]
    O --> P[定时任务重试]
```

## 3. 发票系统集成

### 3.1 发票开票流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant I as 发票服务
    participant T as 第三方开票系统
    participant E as 邮件服务
    participant F as 文件服务
    
    U->>I: 申请发票
    I->>I: 验证申请信息
    I->>I: 生成发票申请单
    
    I->>T: 调用开票接口
    T->>T: 生成发票
    T-->>I: 返回发票信息
    
    I->>F: 下载发票文件
    F-->>I: 返回文件URL
    
    I->>E: 发送发票邮件
    E-->>I: 发送成功确认
    
    I->>I: 更新开票状态
    I-->>U: 返回开票结果
```

### 3.2 发票接口适配器

```java
/**
 * 发票开票适配器
 */
@Component
public class InvoiceAdapter {
    
    @Autowired
    private InvoiceApiClient invoiceApiClient;
    
    /**
     * 申请开票
     */
    public InvoiceCreateResult createInvoice(InvoiceCreateRequest request) {
        try {
            // 1. 构建开票请求参数
            ThirdPartyInvoiceRequest apiRequest = buildInvoiceRequest(request);
            
            // 2. 调用第三方开票接口
            ThirdPartyInvoiceResponse apiResponse = invoiceApiClient.createInvoice(apiRequest);
            
            // 3. 处理开票结果
            if (apiResponse.isSuccess()) {
                return InvoiceCreateResult.builder()
                    .success(true)
                    .invoiceNo(apiResponse.getInvoiceNo())
                    .invoiceUrl(apiResponse.getInvoiceUrl())
                    .invoiceCode(apiResponse.getInvoiceCode())
                    .build();
            } else {
                return InvoiceCreateResult.builder()
                    .success(false)
                    .errorMessage(apiResponse.getErrorMessage())
                    .build();
            }
        } catch (Exception e) {
            log.error("调用开票接口异常", e);
            return InvoiceCreateResult.builder()
                .success(false)
                .errorMessage("开票系统异常，请稍后重试")
                .build();
        }
    }
    
    /**
     * 查询发票状态
     */
    public InvoiceQueryResult queryInvoice(String invoiceNo) {
        // 查询发票开票状态和下载链接
        return invoiceApiClient.queryInvoice(invoiceNo);
    }
}
```

## 4. 通知系统集成

### 4.1 通知发送架构

```mermaid
flowchart TD
    A[业务事件] --> B[消息队列]
    B --> C[通知服务]
    C --> D{通知类型}
    
    D -->|短信通知| E[短信服务适配器]
    D -->|邮件通知| F[邮件服务适配器]
    D -->|站内消息| G[站内消息服务]
    D -->|推送通知| H[推送服务适配器]
    
    E --> I[第三方短信平台]
    F --> J[第三方邮件平台]
    G --> K[消息存储]
    H --> L[推送平台]
    
    I --> M[发送结果回调]
    J --> M
    K --> M
    L --> M
    
    M --> N[更新发送状态]
    N --> O[记录发送日志]
```

### 4.2 通知模板管理

```java
/**
 * 通知模板管理器
 */
@Component
public class NotificationTemplateManager {
    
    private static final Map<String, NotificationTemplate> TEMPLATES = Map.of(
        "ORDER_PAID", NotificationTemplate.builder()
            .templateCode("ORDER_PAID")
            .smsTemplate("您的订单${orderNo}已支付成功，金额${amount}元")
            .emailTemplate("订单支付成功通知")
            .emailContent("尊敬的用户，您的订单${orderNo}已支付成功...")
            .build(),
        
        "INVOICE_GENERATED", NotificationTemplate.builder()
            .templateCode("INVOICE_GENERATED")
            .smsTemplate("您申请的发票已开具完成，请查收邮件")
            .emailTemplate("发票开具完成通知")
            .emailContent("尊敬的用户，您申请的发票已开具完成...")
            .build()
    );
    
    /**
     * 获取通知模板
     */
    public NotificationTemplate getTemplate(String templateCode) {
        return TEMPLATES.get(templateCode);
    }
    
    /**
     * 渲染模板内容
     */
    public String renderTemplate(String template, Map<String, Object> params) {
        String result = template;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            result = result.replace("${" + entry.getKey() + "}", 
                String.valueOf(entry.getValue()));
        }
        return result;
    }
}
```

## 5. 内部系统集成

### 5.1 用户权限系统集成

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as 网关
    participant A as 认证服务
    participant P as 付费服务
    participant U as 用户服务
    
    C->>G: 请求付费接口
    G->>A: 验证JWT Token
    A->>U: 获取用户权限
    U-->>A: 返回权限信息
    A-->>G: 返回认证结果
    
    alt 认证成功
        G->>P: 转发业务请求
        P->>P: 执行业务逻辑
        P-->>G: 返回业务结果
        G-->>C: 返回响应
    else 认证失败
        G-->>C: 返回401错误
    end
```

### 5.2 门店管理系统集成

```java
/**
 * 门店服务客户端
 */
@FeignClient(name = "hotel-service", path = "/api/v1/hotels")
public interface HotelServiceClient {
    
    /**
     * 获取门店信息
     */
    @GetMapping("/{hotelCode}")
    Result<HotelInfo> getHotelInfo(@PathVariable String hotelCode);
    
    /**
     * 更新门店服务状态
     */
    @PostMapping("/{hotelCode}/service-status")
    Result<Void> updateServiceStatus(@PathVariable String hotelCode, 
                                   @RequestBody ServiceStatusUpdateRequest request);
    
    /**
     * 批量查询门店信息
     */
    @PostMapping("/batch-query")
    Result<List<HotelInfo>> batchQueryHotels(@RequestBody List<String> hotelCodes);
}

/**
 * 门店服务集成
 */
@Service
public class HotelIntegrationService {
    
    @Autowired
    private HotelServiceClient hotelServiceClient;
    
    /**
     * 订单支付成功后更新门店服务状态
     */
    @EventListener
    public void handleOrderPaidEvent(OrderPaidEvent event) {
        try {
            // 1. 获取订单信息
            PaymentOrder order = orderService.getByOrderNo(event.getOrderNo());
            
            // 2. 计算服务时间
            LocalDateTime serviceStartTime = LocalDateTime.now();
            LocalDateTime serviceEndTime = calculateServiceEndTime(
                serviceStartTime, order.getPurchasePeriod());
            
            // 3. 更新门店服务状态
            ServiceStatusUpdateRequest request = ServiceStatusUpdateRequest.builder()
                .serviceType("PAYMENT_PACKAGE")
                .serviceStatus("ACTIVE")
                .serviceStartTime(serviceStartTime)
                .serviceEndTime(serviceEndTime)
                .packageId(order.getHotelPackageId())
                .build();
            
            hotelServiceClient.updateServiceStatus(order.getHotelCode(), request);
            
        } catch (Exception e) {
            log.error("更新门店服务状态失败", e);
            // 发送补偿消息
            compensationService.sendCompensationMessage(event);
        }
    }
}
```

## 6. 消息队列集成

### 6.1 消息主题设计

```mermaid
graph TB
    subgraph "业务事件"
        A[订单创建]
        B[订单支付]
        C[订单取消]
        D[发票申请]
        E[发票开具]
    end
    
    subgraph "消息主题"
        F[order_created]
        G[order_paid]
        H[order_cancelled]
        I[invoice_applied]
        J[invoice_generated]
    end
    
    subgraph "消费者服务"
        K[通知服务]
        L[门店服务]
        M[统计服务]
        N[审计服务]
    end
    
    A --> F
    B --> G
    C --> H
    D --> I
    E --> J
    
    F --> K
    F --> N
    G --> K
    G --> L
    G --> M
    H --> K
    H --> N
    I --> K
    I --> N
    J --> K
    J --> M
```

### 6.2 消息处理机制

```java
/**
 * 订单支付成功消息处理器
 */
@Component
@RocketMQMessageListener(
    topic = "order_paid",
    consumerGroup = "payment-service-group",
    messageModel = MessageModel.CLUSTERING
)
public class OrderPaidMessageListener implements RocketMQListener<OrderPaidMessage> {
    
    @Override
    public void onMessage(OrderPaidMessage message) {
        try {
            log.info("收到订单支付成功消息: {}", message);
            
            // 1. 创建账单记录
            billService.createBillFromOrder(message.getOrderNo());
            
            // 2. 更新门店服务状态
            hotelIntegrationService.updateServiceStatus(message);
            
            // 3. 发送支付成功通知
            notificationService.sendPaymentSuccessNotification(message);
            
            // 4. 更新统计数据
            statisticsService.updatePaymentStatistics(message);
            
        } catch (Exception e) {
            log.error("处理订单支付成功消息失败", e);
            throw new RuntimeException("消息处理失败", e);
        }
    }
}

/**
 * 消息重试和死信处理
 */
@Component
public class MessageRetryHandler {
    
    /**
     * 处理消息重试
     */
    @RocketMQMessageListener(
        topic = "order_paid",
        consumerGroup = "payment-retry-group",
        maxReconsumeTimes = 3
    )
    public class RetryMessageListener implements RocketMQListener<OrderPaidMessage> {
        
        @Override
        public void onMessage(OrderPaidMessage message) {
            // 重试处理逻辑
        }
    }
    
    /**
     * 处理死信消息
     */
    @RocketMQMessageListener(
        topic = "%DLQ%order_paid",
        consumerGroup = "payment-dlq-group"
    )
    public class DeadLetterMessageListener implements RocketMQListener<OrderPaidMessage> {
        
        @Override
        public void onMessage(OrderPaidMessage message) {
            // 死信消息处理：记录日志、人工介入等
            log.error("收到死信消息，需要人工处理: {}", message);
            alertService.sendDeadLetterAlert(message);
        }
    }
}
```

## 7. 集成监控和运维

### 7.1 集成健康检查

```java
/**
 * 集成健康检查
 */
@Component
public class IntegrationHealthIndicator implements HealthIndicator {
    
    @Autowired
    private PaymentAdapter wechatPaymentAdapter;
    
    @Autowired
    private InvoiceAdapter invoiceAdapter;
    
    @Autowired
    private HotelServiceClient hotelServiceClient;
    
    @Override
    public Health health() {
        Health.Builder builder = Health.up();
        
        // 检查支付接口
        try {
            // 调用支付接口健康检查
            builder.withDetail("wechat_payment", "UP");
        } catch (Exception e) {
            builder.withDetail("wechat_payment", "DOWN");
            builder.down();
        }
        
        // 检查发票接口
        try {
            // 调用发票接口健康检查
            builder.withDetail("invoice_service", "UP");
        } catch (Exception e) {
            builder.withDetail("invoice_service", "DOWN");
            builder.down();
        }
        
        // 检查内部服务
        try {
            hotelServiceClient.getHotelInfo("HEALTH_CHECK");
            builder.withDetail("hotel_service", "UP");
        } catch (Exception e) {
            builder.withDetail("hotel_service", "DOWN");
            builder.down();
        }
        
        return builder.build();
    }
}
```

### 7.2 集成监控指标

```java
/**
 * 集成监控指标
 */
@Component
public class IntegrationMetrics {
    
    private final Counter paymentCallCounter;
    private final Timer paymentCallTimer;
    private final Counter invoiceCallCounter;
    private final Timer invoiceCallTimer;
    
    public IntegrationMetrics(MeterRegistry meterRegistry) {
        this.paymentCallCounter = Counter.builder("payment.api.calls")
            .description("支付接口调用次数")
            .register(meterRegistry);
            
        this.paymentCallTimer = Timer.builder("payment.api.duration")
            .description("支付接口调用耗时")
            .register(meterRegistry);
            
        this.invoiceCallCounter = Counter.builder("invoice.api.calls")
            .description("发票接口调用次数")
            .register(meterRegistry);
            
        this.invoiceCallTimer = Timer.builder("invoice.api.duration")
            .description("发票接口调用耗时")
            .register(meterRegistry);
    }
    
    public void recordPaymentCall() {
        paymentCallCounter.increment();
    }
    
    public void recordPaymentDuration(Duration duration) {
        paymentCallTimer.record(duration);
    }
}
```

这个系统集成设计文档详细描述了付费管理系统与各个外部和内部系统的集成方案，包括接口设计、消息处理、监控运维等关键内容。
