# 付费管理系统 - 价格计算详细设计

## 1. 价格计算概述

### 1.1 计算维度
- **套餐维度**: 基于套餐的优惠模式（折扣/一口价）
- **产品维度**: 每个产品的独立定价
- **周期维度**: 月度/季度/年度不同周期
- **门店维度**: 门店个性化定价覆盖

### 1.2 价格类型
- **门市价**: 产品的标准市场价格
- **优惠价**: 经过折扣或一口价处理后的价格
- **最终价**: 考虑周期折扣后的实际价格
- **个性化价**: 门店自定义的特殊价格

## 2. 价格计算核心流程

### 2.1 整体计算流程

```mermaid
flowchart TD
    A[开始价格计算] --> B[获取套餐基本信息]
    B --> C[获取套餐优惠模式]
    C --> D[获取产品列表]
    D --> E[遍历计算每个产品价格]
    
    E --> F{套餐优惠模式}
    F -->|折扣模式| G[按折扣比例计算]
    F -->|一口价模式| H[使用固定价格]
    
    G --> I[产品优惠价 = 门市价 × (1 - 折扣率)]
    H --> J[产品优惠价 = 一口价]
    
    I --> K[检查门店个性化定价]
    J --> K
    
    K --> L{是否有个性化定价}
    L -->|是| M[使用门店自定义价格]
    L -->|否| N[使用套餐默认价格]
    
    M --> O[累加所有产品价格]
    N --> O
    
    O --> P[应用购买周期折扣]
    P --> Q[计算最终订单金额]
    Q --> R[返回价格明细]
```

### 2.2 产品价格计算详细流程

```mermaid
flowchart TD
    A[单个产品价格计算] --> B[获取产品基础定价]
    B --> C[获取产品所属套餐信息]
    C --> D{套餐优惠模式判断}
    
    D -->|折扣模式| E[获取折扣比例配置]
    D -->|一口价模式| F[获取一口价配置]
    
    E --> G[计算折扣价格]
    F --> H[使用一口价]
    
    G --> I{是否有门店个性化配置}
    H --> I
    
    I -->|有个性化配置| J[检查个性化定价类型]
    I -->|无个性化配置| K[使用套餐默认价格]
    
    J --> L{个性化定价类型}
    L -->|自定义门市价| M[使用自定义门市价重新计算]
    L -->|自定义优惠价| N[直接使用自定义优惠价]
    L -->|自定义折扣率| O[使用自定义折扣率计算]
    
    M --> P[重新应用套餐优惠模式]
    N --> Q[直接使用自定义价格]
    O --> R[门市价 × 自定义折扣率]
    
    P --> S[得到产品最终价格]
    Q --> S
    R --> S
    K --> S
    
    S --> T[返回产品价格结果]
```

## 3. 价格计算算法实现

### 3.1 核心计算类设计

```java
/**
 * 价格计算引擎
 */
@Component
public class PriceCalculationEngine {
    
    /**
     * 计算套餐总价
     */
    public PriceCalculationResult calculatePackagePrice(PriceCalculationRequest request) {
        // 1. 获取套餐信息
        PaymentPackage packageInfo = packageService.getById(request.getPackageId());
        
        // 2. 获取产品列表
        List<PaymentProduct> products = productService.getByPackageId(request.getPackageId());
        
        // 3. 计算每个产品价格
        List<ProductPriceDetail> productPrices = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (PaymentProduct product : products) {
            ProductPriceDetail priceDetail = calculateProductPrice(
                product, packageInfo, request);
            productPrices.add(priceDetail);
            totalAmount = totalAmount.add(priceDetail.getFinalPrice());
        }
        
        // 4. 应用周期折扣
        PeriodDiscountResult periodResult = applyPeriodDiscount(
            totalAmount, request.getPurchasePeriod());
        
        // 5. 构建返回结果
        return PriceCalculationResult.builder()
            .originalAmount(totalAmount)
            .discountAmount(periodResult.getDiscountAmount())
            .finalAmount(periodResult.getFinalAmount())
            .productPrices(productPrices)
            .periodDiscount(periodResult.getDiscountRate())
            .build();
    }
    
    /**
     * 计算单个产品价格
     */
    private ProductPriceDetail calculateProductPrice(
            PaymentProduct product, 
            PaymentPackage packageInfo, 
            PriceCalculationRequest request) {
        
        // 1. 获取产品基础定价
        ProductPricing basePricing = pricingService.getProductPricing(
            product.getId(), request.getPurchasePeriod());
        
        // 2. 检查门店个性化定价
        HotelProductPricing customPricing = null;
        if (StringUtils.isNotBlank(request.getHotelCode())) {
            customPricing = hotelPricingService.getCustomPricing(
                request.getHotelCode(), product.getId(), request.getPurchasePeriod());
        }
        
        // 3. 计算最终价格
        BigDecimal finalPrice;
        if (customPricing != null && customPricing.getIsCustomPricing() == 1) {
            // 使用个性化定价
            finalPrice = calculateCustomPrice(customPricing, basePricing, packageInfo);
        } else {
            // 使用默认定价
            finalPrice = calculateDefaultPrice(basePricing, packageInfo);
        }
        
        return ProductPriceDetail.builder()
            .productId(product.getId())
            .productName(product.getProductName())
            .marketPrice(basePricing.getMarketPrice())
            .finalPrice(finalPrice)
            .isCustomPricing(customPricing != null && customPricing.getIsCustomPricing() == 1)
            .build();
    }
}
```

### 3.2 价格计算策略模式

```java
/**
 * 价格计算策略接口
 */
public interface PriceCalculationStrategy {
    BigDecimal calculate(PriceCalculationContext context);
    boolean supports(Integer discountMode);
}

/**
 * 折扣模式计算策略
 */
@Component
public class DiscountModeStrategy implements PriceCalculationStrategy {
    
    @Override
    public BigDecimal calculate(PriceCalculationContext context) {
        BigDecimal marketPrice = context.getMarketPrice();
        BigDecimal discountRate = context.getDiscountRate();
        
        if (discountRate == null) {
            return marketPrice;
        }
        
        // 计算折扣价格：门市价 × (1 - 折扣率)
        BigDecimal discountMultiplier = BigDecimal.ONE.subtract(
            discountRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
        
        return marketPrice.multiply(discountMultiplier)
            .setScale(2, RoundingMode.HALF_UP);
    }
    
    @Override
    public boolean supports(Integer discountMode) {
        return discountMode != null && discountMode == 1; // 折扣模式
    }
}

/**
 * 一口价模式计算策略
 */
@Component
public class FixedPriceModeStrategy implements PriceCalculationStrategy {
    
    @Override
    public BigDecimal calculate(PriceCalculationContext context) {
        BigDecimal fixedPrice = context.getDiscountPrice();
        
        if (fixedPrice == null) {
            // 如果没有设置一口价，使用门市价
            return context.getMarketPrice();
        }
        
        return fixedPrice;
    }
    
    @Override
    public boolean supports(Integer discountMode) {
        return discountMode != null && discountMode == 2; // 一口价模式
    }
}
```

## 4. 周期折扣计算

### 4.1 周期折扣规则

```mermaid
flowchart TD
    A[周期折扣计算] --> B{购买周期}
    B -->|月度| C[无折扣 × 1.0]
    B -->|季度| D[季度折扣 × 0.95]
    B -->|年度| E[年度折扣 × 0.85]
    
    C --> F[月度总价 = 产品价格总和]
    D --> G[季度总价 = 月度总价 × 3 × 0.95]
    E --> H[年度总价 = 月度总价 × 12 × 0.85]
    
    F --> I[返回最终价格]
    G --> I
    H --> I
```

### 4.2 周期折扣实现

```java
/**
 * 周期折扣计算服务
 */
@Service
public class PeriodDiscountService {
    
    // 周期折扣配置
    private static final Map<String, PeriodDiscountConfig> PERIOD_DISCOUNTS = Map.of(
        "MONTHLY", new PeriodDiscountConfig(1, BigDecimal.ONE),
        "QUARTERLY", new PeriodDiscountConfig(3, new BigDecimal("0.95")),
        "YEARLY", new PeriodDiscountConfig(12, new BigDecimal("0.85"))
    );
    
    /**
     * 应用周期折扣
     */
    public PeriodDiscountResult applyPeriodDiscount(
            BigDecimal monthlyAmount, String purchasePeriod) {
        
        PeriodDiscountConfig config = PERIOD_DISCOUNTS.get(purchasePeriod);
        if (config == null) {
            throw new BusinessException("不支持的购买周期: " + purchasePeriod);
        }
        
        // 计算周期总价
        BigDecimal periodAmount = monthlyAmount.multiply(new BigDecimal(config.getMonths()));
        
        // 应用周期折扣
        BigDecimal finalAmount = periodAmount.multiply(config.getDiscountRate())
            .setScale(2, RoundingMode.HALF_UP);
        
        // 计算折扣金额
        BigDecimal discountAmount = periodAmount.subtract(finalAmount);
        
        return PeriodDiscountResult.builder()
            .originalAmount(periodAmount)
            .discountAmount(discountAmount)
            .finalAmount(finalAmount)
            .discountRate(config.getDiscountRate())
            .months(config.getMonths())
            .build();
    }
}
```

## 5. 个性化定价处理

### 5.1 个性化定价流程

```mermaid
flowchart TD
    A[检查个性化定价] --> B[查询门店套餐配置]
    B --> C{是否存在个性化配置}
    C -->|否| D[使用套餐默认价格]
    C -->|是| E[获取个性化定价配置]
    
    E --> F{个性化定价类型}
    F -->|自定义门市价| G[使用自定义门市价]
    F -->|自定义优惠价| H[直接使用自定义优惠价]
    F -->|自定义折扣率| I[使用自定义折扣率]
    
    G --> J[重新应用套餐优惠模式]
    H --> K[直接返回自定义价格]
    I --> L[门市价 × 自定义折扣率]
    
    J --> M[得到最终价格]
    K --> M
    L --> M
    D --> M
    
    M --> N[返回价格结果]
```

### 5.2 个性化定价实现

```java
/**
 * 个性化定价处理服务
 */
@Service
public class CustomPricingService {
    
    /**
     * 计算个性化价格
     */
    public BigDecimal calculateCustomPrice(
            HotelProductPricing customPricing,
            ProductPricing basePricing,
            PaymentPackage packageInfo) {
        
        // 如果直接设置了自定义优惠价，直接使用
        if (customPricing.getCustomDiscountPrice() != null) {
            return customPricing.getCustomDiscountPrice();
        }
        
        // 如果设置了自定义门市价，重新计算
        if (customPricing.getCustomMarketPrice() != null) {
            return calculateWithCustomMarketPrice(
                customPricing.getCustomMarketPrice(), packageInfo);
        }
        
        // 如果设置了自定义折扣率，使用原门市价计算
        if (customPricing.getCustomDiscountRate() != null) {
            return calculateWithCustomDiscountRate(
                basePricing.getMarketPrice(), customPricing.getCustomDiscountRate());
        }
        
        // 默认使用套餐价格
        return basePricing.getFinalPrice();
    }
    
    /**
     * 使用自定义门市价计算
     */
    private BigDecimal calculateWithCustomMarketPrice(
            BigDecimal customMarketPrice, PaymentPackage packageInfo) {
        
        if (packageInfo.getDiscountMode() == 1) {
            // 折扣模式：需要获取折扣率重新计算
            // 这里简化处理，实际需要获取具体的折扣率配置
            return customMarketPrice.multiply(new BigDecimal("0.8"))
                .setScale(2, RoundingMode.HALF_UP);
        } else {
            // 一口价模式：直接使用自定义门市价
            return customMarketPrice;
        }
    }
    
    /**
     * 使用自定义折扣率计算
     */
    private BigDecimal calculateWithCustomDiscountRate(
            BigDecimal marketPrice, BigDecimal customDiscountRate) {
        
        BigDecimal discountMultiplier = BigDecimal.ONE.subtract(
            customDiscountRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
        
        return marketPrice.multiply(discountMultiplier)
            .setScale(2, RoundingMode.HALF_UP);
    }
}
```

## 6. 价格计算结果模型

### 6.1 计算结果数据结构

```java
/**
 * 价格计算结果
 */
@Data
@Builder
public class PriceCalculationResult {
    
    /**
     * 原始金额（所有产品月度价格总和）
     */
    private BigDecimal originalAmount;
    
    /**
     * 周期折扣金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 最终金额
     */
    private BigDecimal finalAmount;
    
    /**
     * 购买周期
     */
    private String purchasePeriod;
    
    /**
     * 周期折扣率
     */
    private BigDecimal periodDiscountRate;
    
    /**
     * 产品价格明细
     */
    private List<ProductPriceDetail> productPrices;
    
    /**
     * 计算时间
     */
    private LocalDateTime calculationTime;
}

/**
 * 产品价格明细
 */
@Data
@Builder
public class ProductPriceDetail {
    
    /**
     * 产品ID
     */
    private Long productId;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 门市价
     */
    private BigDecimal marketPrice;
    
    /**
     * 最终价格
     */
    private BigDecimal finalPrice;
    
    /**
     * 是否使用个性化定价
     */
    private Boolean isCustomPricing;
    
    /**
     * 价格计算说明
     */
    private String priceDescription;
}
```

## 7. 价格计算测试用例

### 7.1 测试场景设计

```mermaid
flowchart TD
    A[价格计算测试] --> B[基础场景测试]
    A --> C[个性化定价测试]
    A --> D[周期折扣测试]
    A --> E[边界条件测试]
    
    B --> F[折扣模式计算]
    B --> G[一口价模式计算]
    
    C --> H[自定义门市价]
    C --> I[自定义优惠价]
    C --> J[自定义折扣率]
    
    D --> K[月度无折扣]
    D --> L[季度95折]
    D --> M[年度85折]
    
    E --> N[价格为0]
    E --> O[折扣率100%]
    E --> P[负数价格处理]
```

### 7.2 单元测试示例

```java
@SpringBootTest
class PriceCalculationEngineTest {
    
    @Autowired
    private PriceCalculationEngine priceCalculationEngine;
    
    @Test
    @DisplayName("测试折扣模式价格计算")
    void testDiscountModeCalculation() {
        // 准备测试数据
        PriceCalculationRequest request = PriceCalculationRequest.builder()
            .packageId(1L)
            .purchasePeriod("MONTHLY")
            .hotelCode("HOTEL_001")
            .build();
        
        // 执行计算
        PriceCalculationResult result = priceCalculationEngine.calculatePackagePrice(request);
        
        // 验证结果
        assertThat(result.getFinalAmount()).isEqualTo(new BigDecimal("800.00"));
        assertThat(result.getProductPrices()).hasSize(2);
    }
    
    @Test
    @DisplayName("测试个性化定价覆盖")
    void testCustomPricingOverride() {
        // 测试个性化定价是否正确覆盖默认价格
    }
    
    @Test
    @DisplayName("测试周期折扣计算")
    void testPeriodDiscountCalculation() {
        // 测试不同周期的折扣计算是否正确
    }
}
```

这个价格计算设计文档详细描述了整个价格计算的核心逻辑，包括流程图、算法实现和测试用例，为开发提供了完整的技术指导。
