# AI运营后台付费管理系统 - 后端技术方案

## 概述

本文档描述了基于需求文档和数据库表结构设计的后端技术架构方案，采用微服务架构，支持高并发、高可用的付费管理业务需求。

## 技术栈选型

### 1. 核心框架
- **Spring Boot 3.2+**: 主框架，提供自动配置和快速开发
- **Spring Cloud 2023.x**: 微服务治理框架
- **Spring Security 6.x**: 安全认证授权框架
- **MyBatis-Plus 3.5+**: ORM框架，简化数据库操作

### 2. 数据存储
- **MySQL 8.0+**: 主数据库，存储业务数据
- **Redis 7.x**: 缓存数据库，存储会话、缓存等
- **MongoDB 6.x**: 文档数据库，存储日志、配置等

### 3. 消息队列
- **RocketMQ 5.x**: 消息队列，处理异步业务
- **WebSocket**: 实时通知推送

### 4. 基础设施
- **Nacos 2.3+**: 服务注册发现、配置管理
- **Gateway**: API网关，统一入口
- **Docker + K8s**: 容器化部署
- **Jenkins**: CI/CD自动化部署

## 系统架构设计

### 1. 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        前端应用层                              │
├─────────────────────────────────────────────────────────────┤
│                     API网关层 (Gateway)                       │
├─────────────────────────────────────────────────────────────┤
│  套餐管理服务  │  订单管理服务  │  支付管理服务  │  发票管理服务    │
│  Package      │  Order        │  Payment      │  Invoice      │
│  Service      │  Service      │  Service      │  Service      │
├─────────────────────────────────────────────────────────────┤
│           基础服务层 (用户服务、权限服务、通知服务)              │
├─────────────────────────────────────────────────────────────┤
│  MySQL 8.0+   │  Redis 7.x    │  RocketMQ 5.x │  MongoDB 6.x  │
└─────────────────────────────────────────────────────────────┘
```

### 2. 微服务拆分

#### 核心业务服务
1. **套餐管理服务 (package-service)**
   - 套餐CRUD管理
   - 产品管理
   - 定价管理
   - 门店套餐配置

2. **订单管理服务 (order-service)**
   - 订单创建与管理
   - 订单状态流转
   - 订单查询统计

3. **支付管理服务 (payment-service)**
   - 支付接口集成
   - 账单管理
   - 退款处理

4. **发票管理服务 (invoice-service)**
   - 发票申请管理
   - 发票审核流程
   - 发票生成与推送

#### 基础支撑服务
1. **用户权限服务 (auth-service)**
   - 用户认证授权
   - 权限管理
   - JWT令牌管理

2. **通知服务 (notification-service)**
   - 消息推送
   - 邮件发送
   - 短信通知

3. **文件服务 (file-service)**
   - 文件上传下载
   - 发票文件管理

## 详细技术设计

### 1. 套餐管理服务设计

#### 1.1 服务职责
- 套餐的增删改查
- 产品管理和定价管理
- 门店套餐配置和个性化定价
- 套餐推荐机制

#### 1.2 核心接口设计
```java
@RestController
@RequestMapping("/api/v1/packages")
public class PackageController {
    
    /**
     * 创建套餐
     */
    @PostMapping
    public Result<PackageVO> createPackage(@RequestBody @Valid CreatePackageRequest request);
    
    /**
     * 查询套餐列表
     */
    @GetMapping
    public Result<PageResult<PackageVO>> getPackages(@Valid PackageQueryRequest request);
    
    /**
     * 获取套餐详情
     */
    @GetMapping("/{packageId}")
    public Result<PackageDetailVO> getPackageDetail(@PathVariable Long packageId);
    
    /**
     * 更新套餐
     */
    @PutMapping("/{packageId}")
    public Result<Void> updatePackage(@PathVariable Long packageId, 
                                     @RequestBody @Valid UpdatePackageRequest request);
    
    /**
     * 删除套餐
     */
    @DeleteMapping("/{packageId}")
    public Result<Void> deletePackage(@PathVariable Long packageId);
    
    /**
     * 设置推荐套餐
     */
    @PostMapping("/{packageId}/recommend")
    public Result<Void> setRecommendedPackage(@PathVariable Long packageId);
}

@RestController
@RequestMapping("/api/v1/hotel-packages")
public class HotelPackageController {
    
    /**
     * 门店配置套餐
     */
    @PostMapping
    public Result<HotelPackageVO> configureHotelPackage(@RequestBody @Valid ConfigureHotelPackageRequest request);
    
    /**
     * 查询门店套餐配置
     */
    @GetMapping
    public Result<List<HotelPackageVO>> getHotelPackages(@RequestParam String hotelCode);
    
    /**
     * 设置个性化定价
     */
    @PostMapping("/{hotelPackageId}/pricing")
    public Result<Void> setCustomPricing(@PathVariable Long hotelPackageId,
                                        @RequestBody @Valid CustomPricingRequest request);
}
```

#### 1.3 数据模型设计
```java
// 套餐实体
@Data
@TableName("hds_payment_package")
public class PaymentPackage {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String packageCode;
    private String packageName;
    private Integer paymentMode;  // 0-按房间数量，1-按门店
    private Integer discountMode; // 1-折扣，2-一口价
    private Integer status;       // 1-启用，0-停用
    private Integer isRecommended; // 1-推荐，0-非推荐
    // ... 审计字段
}

// 产品实体
@Data
@TableName("hds_payment_product")
public class PaymentProduct {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long packageId;
    private String productName;
    private String productDescription; // JSON格式
    private Integer sortOrder;
    // ... 审计字段
}

// 定价实体
@Data
@TableName("hds_product_pricing")
public class ProductPricing {
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long productId;
    private String periodType; // MONTHLY, QUARTERLY, YEARLY
    private BigDecimal marketPrice;
    private BigDecimal discountPrice;
    private BigDecimal discountRate;
    private BigDecimal finalPrice;
    // ... 审计字段
}
```

### 2. 订单管理服务设计

#### 2.1 服务职责
- 订单创建和管理
- 订单状态流转
- 订单查询和统计
- 订单超时处理

#### 2.2 核心接口设计
```java
@RestController
@RequestMapping("/api/v1/orders")
public class OrderController {
    
    /**
     * 创建订单
     */
    @PostMapping
    public Result<OrderVO> createOrder(@RequestBody @Valid CreateOrderRequest request);
    
    /**
     * 查询订单列表
     */
    @GetMapping
    public Result<PageResult<OrderVO>> getOrders(@Valid OrderQueryRequest request);
    
    /**
     * 获取订单详情
     */
    @GetMapping("/{orderNo}")
    public Result<OrderDetailVO> getOrderDetail(@PathVariable String orderNo);
    
    /**
     * 取消订单
     */
    @PostMapping("/{orderNo}/cancel")
    public Result<Void> cancelOrder(@PathVariable String orderNo);
    
    /**
     * 订单统计
     */
    @GetMapping("/statistics")
    public Result<OrderStatisticsVO> getOrderStatistics(@Valid OrderStatisticsRequest request);
}
```

#### 2.3 订单状态机设计
```java
@Component
public class OrderStateMachine {
    
    /**
     * 订单状态流转
     */
    public enum OrderStatus {
        PENDING("待支付"),
        PAID("已支付"),
        CANCELLED("已取消"),
        EXPIRED("已过期");
    }
    
    /**
     * 状态流转规则
     */
    private static final Map<OrderStatus, Set<OrderStatus>> STATE_TRANSITIONS = Map.of(
        OrderStatus.PENDING, Set.of(OrderStatus.PAID, OrderStatus.CANCELLED, OrderStatus.EXPIRED),
        OrderStatus.PAID, Set.of(), // 已支付状态不可变更
        OrderStatus.CANCELLED, Set.of(),
        OrderStatus.EXPIRED, Set.of()
    );
    
    public boolean canTransition(OrderStatus from, OrderStatus to) {
        return STATE_TRANSITIONS.getOrDefault(from, Set.of()).contains(to);
    }
}
```

### 3. 支付管理服务设计

#### 3.1 服务职责
- 支付接口集成（微信、支付宝）
- 支付回调处理
- 账单管理
- 退款处理

#### 3.2 支付接口设计
```java
@RestController
@RequestMapping("/api/v1/payments")
public class PaymentController {
    
    /**
     * 发起支付
     */
    @PostMapping("/pay")
    public Result<PaymentVO> createPayment(@RequestBody @Valid CreatePaymentRequest request);
    
    /**
     * 支付回调
     */
    @PostMapping("/callback/{paymentMethod}")
    public String paymentCallback(@PathVariable String paymentMethod, 
                                 HttpServletRequest request);
    
    /**
     * 查询支付状态
     */
    @GetMapping("/status/{orderNo}")
    public Result<PaymentStatusVO> getPaymentStatus(@PathVariable String orderNo);
    
    /**
     * 申请退款
     */
    @PostMapping("/refund")
    public Result<RefundVO> createRefund(@RequestBody @Valid CreateRefundRequest request);
}
```

#### 3.3 支付策略模式
```java
// 支付策略接口
public interface PaymentStrategy {
    PaymentResult createPayment(PaymentRequest request);
    boolean verifyCallback(HttpServletRequest request);
    RefundResult createRefund(RefundRequest request);
}

// 微信支付策略
@Component("wechatPayment")
public class WechatPaymentStrategy implements PaymentStrategy {
    // 微信支付实现
}

// 支付宝支付策略
@Component("alipayPayment")
public class AlipayPaymentStrategy implements PaymentStrategy {
    // 支付宝支付实现
}

// 支付工厂
@Component
public class PaymentFactory {
    
    @Autowired
    private Map<String, PaymentStrategy> paymentStrategies;
    
    public PaymentStrategy getPaymentStrategy(String paymentMethod) {
        return paymentStrategies.get(paymentMethod + "Payment");
    }
}
```

### 4. 发票管理服务设计

#### 4.1 服务职责
- 发票信息管理
- 发票申请处理
- 发票审核流程
- 发票生成和推送

#### 4.2 核心接口设计
```java
@RestController
@RequestMapping("/api/v1/invoices")
public class InvoiceController {
    
    /**
     * 保存发票信息
     */
    @PostMapping("/info")
    public Result<Void> saveInvoiceInfo(@RequestBody @Valid SaveInvoiceInfoRequest request);
    
    /**
     * 申请发票
     */
    @PostMapping("/applications")
    public Result<InvoiceApplicationVO> createInvoiceApplication(
        @RequestBody @Valid CreateInvoiceApplicationRequest request);
    
    /**
     * 查询发票申请列表
     */
    @GetMapping("/applications")
    public Result<PageResult<InvoiceApplicationVO>> getInvoiceApplications(
        @Valid InvoiceApplicationQueryRequest request);
    
    /**
     * 审核发票申请
     */
    @PostMapping("/applications/{applicationId}/review")
    public Result<Void> reviewInvoiceApplication(@PathVariable Long applicationId,
                                               @RequestBody @Valid ReviewInvoiceRequest request);
    
    /**
     * 生成发票
     */
    @PostMapping("/applications/{applicationId}/generate")
    public Result<Void> generateInvoice(@PathVariable Long applicationId);
}
```

#### 4.3 发票审核工作流
```java
@Component
public class InvoiceWorkflow {
    
    public enum InvoiceStatus {
        PENDING("待审核"),
        REVIEWING("审核中"),
        INVOICED("已开票"),
        REJECTED("已驳回");
    }
    
    /**
     * 发票审核流程
     */
    @Async
    public void processInvoiceApplication(Long applicationId) {
        // 1. 验证申请信息
        // 2. 自动审核规则检查
        // 3. 人工审核（如需要）
        // 4. 生成发票
        // 5. 发送通知
    }
}
```

## 技术实现细节

### 1. 数据库设计

#### 1.1 连接池配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 10
      maximum-pool-size: 50
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
```

#### 1.2 MyBatis-Plus配置
```java
@Configuration
public class MybatisPlusConfig {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        // 乐观锁插件
        interceptor.addInnerInterceptor(new OptimisticLockerInnerInterceptor());
        return interceptor;
    }
    
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new MyMetaObjectHandler();
    }
}
```

### 2. 缓存设计

#### 2.1 Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 5
```

#### 2.2 缓存策略
```java
@Service
public class PackageService {
    
    @Cacheable(value = "packages", key = "#packageId")
    public PackageVO getPackageById(Long packageId) {
        // 查询套餐信息
    }
    
    @CacheEvict(value = "packages", key = "#packageId")
    public void updatePackage(Long packageId, UpdatePackageRequest request) {
        // 更新套餐信息
    }
    
    @Cacheable(value = "hotel-packages", key = "#hotelCode")
    public List<HotelPackageVO> getHotelPackages(String hotelCode) {
        // 查询门店套餐配置
    }
}
```

### 3. 消息队列设计

#### 3.1 消息主题设计
```java
public class MessageTopics {
    public static final String ORDER_CREATED = "order_created";
    public static final String ORDER_PAID = "order_paid";
    public static final String ORDER_CANCELLED = "order_cancelled";
    public static final String PAYMENT_SUCCESS = "payment_success";
    public static final String PAYMENT_FAILED = "payment_failed";
    public static final String INVOICE_APPLIED = "invoice_applied";
    public static final String INVOICE_GENERATED = "invoice_generated";
}
```

#### 3.2 消息处理
```java
@Component
@RocketMQMessageListener(topic = "order_paid", consumerGroup = "payment-service")
public class OrderPaidListener implements RocketMQListener<OrderPaidMessage> {
    
    @Override
    public void onMessage(OrderPaidMessage message) {
        // 处理订单支付成功消息
        // 1. 创建账单
        // 2. 更新门店服务状态
        // 3. 发送通知
    }
}
```

### 4. 安全设计

#### 4.1 JWT认证
```java
@Component
public class JwtTokenProvider {
    
    public String createToken(UserDetails userDetails) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + jwtExpirationInMs);
        
        return Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
}
```

#### 4.2 权限控制
```java
@RestController
@PreAuthorize("hasRole('PAYMENT_ADMIN')")
public class PackageController {
    
    @PostMapping
    @PreAuthorize("hasAuthority('package:create')")
    public Result<PackageVO> createPackage(@RequestBody CreatePackageRequest request) {
        // 创建套餐
    }
    
    @GetMapping
    @PreAuthorize("hasAuthority('package:read')")
    public Result<PageResult<PackageVO>> getPackages(PackageQueryRequest request) {
        // 查询套餐
    }
}
```

## 部署架构

### 1. 容器化部署
```dockerfile
FROM openjdk:17-jre-slim

COPY target/payment-service.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 2. K8s部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
    spec:
      containers:
      - name: payment-service
        image: payment-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
```

## 监控与运维

### 1. 应用监控
- **Micrometer + Prometheus**: 应用指标监控
- **Grafana**: 监控面板展示
- **ELK Stack**: 日志收集和分析
- **Jaeger**: 分布式链路追踪

### 2. 健康检查
```java
@Component
public class PaymentServiceHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        // 检查数据库连接
        // 检查Redis连接
        // 检查第三方支付接口
        return Health.up().build();
    }
}
```

## 性能优化策略

### 1. 数据库优化
- **读写分离**: 主库写入，从库读取
- **分库分表**: 按酒店编码分表，支持水平扩展
- **索引优化**: 针对高频查询建立复合索引
- **连接池优化**: 合理配置连接池参数

### 2. 缓存优化
- **多级缓存**: 本地缓存 + Redis缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 采用Cache-Aside模式
- **缓存穿透防护**: 布隆过滤器 + 空值缓存

### 3. 接口优化
- **异步处理**: 耗时操作异步化
- **批量操作**: 支持批量查询和更新
- **分页优化**: 深分页优化，使用游标分页
- **接口限流**: 令牌桶算法限制请求频率

## 安全保障

### 1. 数据安全
- **敏感数据加密**: 支付信息、个人信息加密存储
- **SQL注入防护**: 参数化查询，输入验证
- **XSS防护**: 输出编码，CSP策略
- **CSRF防护**: Token验证

### 2. 接口安全
- **HTTPS**: 全站HTTPS加密传输
- **API签名**: 关键接口签名验证
- **频率限制**: 防止恶意请求
- **IP白名单**: 支付回调IP限制

### 3. 业务安全
- **幂等性**: 关键操作幂等性保证
- **事务一致性**: 分布式事务管理
- **审计日志**: 完整的操作审计
- **异常监控**: 实时异常告警

这个技术方案提供了完整的后端架构设计，支持高并发、高可用的付费管理业务需求。
