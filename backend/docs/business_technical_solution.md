(venv) flx@fanlixing-Mac cursor-free % python3 cursor-auto-register.py

========== 开始执行完整注册流程 ==========


===== 开始执行：创建验证码任务 =====
创建任务响应:
{
  "taskId": "6a8ac7a8-4b67-11f0-9068-c61d475bc41a",
  "errorId": 0
}
接口执行耗时: 0.88秒
提取的taskId: 6a8ac7a8-4b67-11f0-9068-c61d475bc41a
===== 结束执行：创建验证码任务 =====


===== 开始执行：获取验证码结果 =====
等待验证码处理...
获取任务结果尝试 1:
{
  "status": "processing",
  "errorId": 0,
  "errorCode": null,
  "errorDescription": null
}
尝试 1 执行耗时: 0.65秒
任务未完成，等待后重试...
获取任务结果尝试 2:
{
  "solution": {
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "token": "0.TB-mLtabjUoXtZYosSWOcc2AAWMv9XeAwiUDHo5PUmoxD56kbME5JOEkuG6icHKft2TeCanWuHnqmvT0oVzm1Xrkn3IbaKiZgUjPUZxsMM7BqqAFR5F7WB0mNXZGuSmETIcZZZE1ZTBrZXwUlDU384DWJtG4Ru1wqNXRZjGLiKFu4RmD1A41N_sHFXziyB04ntPmGbEWN5vRqBrKDNTGWBj4UlGfywNlbVq18FxglheegVoill1kG8GZyWeSfZyv-atJOCm98V42ZoHlRLfwl_uAP-T4ceWF9735S40M_NQZ2x33rC3zz9Map3h3P0Z9aCIoj3NlWBRC1sfWZGmqfSsYLqQX6PoX5C0-V5iAmFnPgnqy4n3giEufqCws9gcHPNC9Cae4IXRRZHRwPu0h7dhXBlr9zo38dbo-pcCg8sZ_WuMEthCurXIy7oaK8pkglNe3YWuYT5glwL00Ynxa1QDPGUf67VbTDQoHII_EZOmPYhY-uZe6rB9YmGThZDluOBDJybmIjfRj8L8kJtcpXpZn6ymaOUDMNP-XHymkZWxCVKoDZADIin4klOgzHUzxJ_3JtuC09x_M5MfaXfjCk0L4XKHkaAcXr-FKzeERWlmBZ66RyZ5yjYz_koJHp7RTxEFXDWpnCrucD0tQ4a1X4R5mCKqjjhiqjOotM8zdqVaeAsyDZFogzLx2qGs4eW7jG54JTWA1H8wI9FW8cEluYDwKngpWeqBq7r-FrtMiYaSqqCZCwCd8Mip4_6muJ6MuuwGcnnjugHFJdhtIMCtu_vKt49i7JWywUO2wDumGTAaBBobY40q0fvIVRDmMF2DHZCyt0yWukIo8hwJLFG9YJgYzZ2zxN45_tRlIQhbCMfH0phsQ5YQ-2wq8f48huJxcw2Q--OJ8vmZcIjiFpy_ijYZluRd-FJT7p8FYQDqgA4U.lgs0ZTnXfklfCH_81yw2bQ.61107bd1d904179c918947c16b5f3104ead4763501899ef941e8264d19ee7c6c"
  },
  "status": "ready",
  "errorId": 0
}
尝试 2 执行耗时: 0.54秒
成功获取token: 0.TB-mLtabjUoXtZYosSWOcc2AAWMv9XeAwiUDHo5PUmoxD56kbME5JOEkuG6icHKft2TeCanWuHnqmvT0oVzm1Xrkn3IbaKiZgUjPUZxsMM7BqqAFR5F7WB0mNXZGuSmETIcZZZErZXwUlDU384DWJtG4Ru1wqNXRZjGLiKFu4RmD1A41N_sHFXziyB04ntPmGbEWN5vRqBrKDNTGWBj4UlGfywNlbVq18FxglheegVoill1kG8GZyWeSfZyv-atJOCm98V42ZoHlRLfwl_uAP-T4ceWF9735S40M_NQZ2x33rC3zz9Map3h3P0Z9aCIoj3NlWBRC1sfWZGmqfSsYLqQX6PoX5C0-V5iAmFnPgnqy4n3giEufqCws9gcHPNC9Cae4IXRRZHRwPu0h7dhXBlr9zo38dbo-pcCg8sZ_WuMEthCurXIy7oaK8pkglNe3YWuYT5glwL00Ynxa1QDPGUf67VbTDQoHII_EZOmPYhY-uZe6rB9YmGThZDluOBDJybmIjfRj8L8kJtcpXpZn6ymaOUDMNP-XHymkZWxCVKoDZADIin4klOgzHUzxJ_3JtuC09x_M5MfaXfjCk0L4XKHkaAcXr-FKzeERWlmBZ66RyZ5yjYz_koJHp7RTxEFXDWpnCrucD0tQ4a1X4R5mCKqjjhiqjOotM8zdqVaeAsyDZFogzLx2qGs4eW7jG54JTWA1H8wI9FW8cEluYDwKngpWeqBq7r-FrtMiYaSqqCZCwCd8Mip4_6muJ6MuuwGcnnjugHFJdhtIMCtu_vKt49i7JWywUO2wDumGTAaBBobY40q0fvIVRDmMF2DHZCyt0yWukIo8hwJLFG9YJgYzZ2zxN45_tRlIQhbCMfH0phsQ5YQ-2wq8f48huJxcw2Q--OJ8vmZcIjiFpy_ijYZluRd-FJT7p8FYQDqgA4U.lgs0ZTnXfklfCH_81yw2bQ.61107bd1d904179c918947c16b5f3104ead4763501899ef941e8264d19ee7c6c
总接口执行耗时: 11.21秒
===== 结束执行：获取验证码结果 =====


===== 开始执行：提交注册表单 =====
使用以下信息注册:
First Name: ASbOk
Last Name: bzanFsvp
Email: <EMAIL>
使用边界值: --------------------------699169061176269953979093

请求体内容:
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_redirect_uri
Content-Type: text/plain

https://cursor.com/api/auth/callback
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_bot_detection_token
Content-Type: text/plain

0.TB-mLtabjUoXtZYosSWOcc2AAWMv9XeAwiUDHo5PUmoxD56kbME5JOEkuG6icHKft2TeCanWuHnqmvT0oVzm1Xrkn3IbaKiZgUjPUZxsMM7BqqAFR5F7WB0mNXZGuSmETIcZZZE1ZTBrZXwUlDU384DWJtG4Ru1wqNXRZjGLiKFu4RmD1A41N_sHFXziyB04ntPmGbEWN5vRqBrKDNTGWBj4UlGfywNlbVq18FxglheegVoill1kG8GZyWeSfZyv-atJOCm98V42ZoHlRLfwl_uAP-T4ceWF9735S40M_NQZ2x33rC3zz9Map3h3P0Z9aCIoj3NlWBRC1sfWZGmqfSsYLqQX6PoX5C0-V5iAmFnPgnqy4n3giEufqCws9gcHPNC9Cae4IXRRZHRwPu0h7dhXBlr9zo38dbo-pcCg8sZ_WuMEthCurXIy7oaK8pkglNe3YWuYT5glwL00Ynxa1QDPGUf67VbTDQoHII_EZOmPYhY-uZe6rB9YmGThZDluOBDJybmIjfRj8L8kJtcpXpZn6ymaOUDMNP-XHymkZWxCVKoDZADIin4klOgzHUzxJ_3JtuC09x_M5MfaXfjCk0L4XKHkaAcXr-FKzeERWlmBZ66RyZ5yjYz_koJHp7RTxEFXDWpnCrucD0tQ4a1X4R5mCKqjjhiqjOotM8zdqVaeAsyDZFogzLx2qGs4eW7jG54JTWA1H8wI9FW8cEluYDwKngpWeqBq7r-FrtMiYaSqqCZCwCd8Mip4_6muJ6MuuwGcnnjugHFJdhtIMCtu_vKt49i7JWywUO2wDumGTAaBBobY40q0fvIVRDmMF2DHZCyt0yWukIo8hwJLFG9YJgYzZ2zxN45_tRlIQhbCMfH0phsQ5YQ-2wq8f48huJxcw2Q--OJ8vmZcIjiFpy_ijYZluRd-FJT7p8FYQDqgA4U.lgs0ZTnXfklfCH_81yw2bQ.61107bd1d904179c918947c16b5f3104ead4763501899ef941e8264d19ee7c6c
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_first_name
Content-Type: text/plain

ASbOk
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_last_name
Content-Type: text/plain

bzanFsvp
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_email
Content-Type: text/plain

<EMAIL>
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_password
Content-Type: text/plain

XGDhBbnI0w
----------------------------699169061176269953979093
Content-Disposition: form-data; name=1_intent
Content-Type: text/plain

sign-up
----------------------------699169061176269953979093
Content-Disposition: form-data; name=0
Content-Type: text/plain

["$K1"]
----------------------------699169061176269953979093--

请求URL: /sign-up/password?first_name=ASbOk&last_name=bzanFsvp&email=<EMAIL>&redirect_uri=https://cursor.com/api/auth/callback

=== 请求详情 ===
请求方法: POST
请求URL: /sign-up/password?first_name=ASbOk&last_name=bzanFsvp&email=<EMAIL>&redirect_uri=https://cursor.com/api/auth/callback
请求头:
  next-action: 770926d8148e29539286d20e1c1548d2aff6c0b9
  User-Agent: Apifox/1.0.0 (https://apifox.com)
  Accept: */*
  Host: authenticator.cursor.sh
  Connection: keep-alive
  Content-Type: multipart/form-data; boundary=--------------------------699169061176269953979093
  Cookie: __cf_bm=sUFME4yDU95zCs.Xzn_kXYoAis3jHevL44F6rOXbd5I-1750077153-*******-DD8abPeTCsR4HeIbyxr2ySeaYIrslLz40wBXEn_S0Kar7euoiA3_sYLLDQcTNaXf1srWpd5QO3jmpgnjLvj0BklhT.jryqyihtImqWm.1Ok; _cfuvid=1ZCkL_r1e7.blyCzV0TzYcLOGI__w1YGhITtFePVBBk-1750077153832-*******-604800000
表单字段:
  1_redirect_uri: https://cursor.com/api/auth/callback
  1_bot_detection_token: 0.TB-mLtabjUoXtZYosSWOcc2AAWMv9XeAwiUDHo5PUmoxD56kbME5JOEkuG6icHKft2TeCanWuHnqmvT0oVzm1Xrkn3IbaKiZgUjPUZxsMM7BqqAFR5F7WB0mNXZGuSmETIcZZZE1ZTBrZXwUlDU384DWJtG4Ru1wqNXRZjGLiKFu4RmD1A41N_sHFXziyB04ntPmGbEWN5vRqBrKDNTGWBj4UlGfywNlbVq18FxglheegVoill1kG8GZyWeSfZyv-atJOCm98V42ZoHlRLfwl_uAP-T4ceWF9735S40M_NQZ2x33rC3zz9Map3h3P0Z9aCIoj3NlWBRC1sfWZGmqfSsYLqQX6PoX5C0-V5iAmFnPgnqy4n3giEufqCws9gcHPNC9Cae4IXRRZHRwPu0h7dhXBlr9zo38dbo-pcCg8sZ_WuMEthCurXIy7oaK8pkglNe3YWuYT5glwL00Ynxa1QDPGUf67VbTDQoHII_EZOmPYhY-uZe6rB9YmGThZDluOBDJybmIjfRj8L8kJtcpXpZn6ymaOUDMNP-XHymkZWxCVKoDZADIin4klOgzHUzxJ_3JtuC09x_M5MfaXfjCk0L4XKHkaAcXr-FKzeERWlmBZ66RyZ5yjYz_koJHp7RTxEFXDWpnCrucD0tQ4a1X4R5mCKqjjhiqjOotM8zdqVaeAsyDZFogzLx2qGs4eW7jG54JTWA1H8wI9FW8cEluYDwKngpWeqBq7r-FrtMiYaSqqCZCwCd8Mip4_6muJ6MuuwGcnnjugHFJdhtIMCtu_vKt49i7JWywUO2wDumGTAaBBobY40q0fvIVRDmMF2DHZCyt0yWukIo8hwJLFG9YJgYzZ2zxN45_tRlIQhbCMfH0phsQ5YQ-2wq8f48huJxcw2Q--OJ8vmZcIjiFpy_ijYZluRd-FJT7p8FYQDqgA4U.lgs0ZTnXfklfCH_81yw2bQ.61107bd1d904179c918947c16b5f3104ead4763501899ef941e8264d19ee7c6c
  1_first_name: ASbOk
  1_last_name: bzanFsvp
  1_email: <EMAIL>
  1_password: XGDhBbnI0w
  1_intent: sign-up
  0: ["$K1"]
=== 请求详情结束 ===


响应状态码: 303
响应内容:
3:I[46896,[],""]
4:I[79604,[],""]
0:["rbz9iXa-W3OEofxXN5b2-",[[["",{"children":["(root)",{"children":["(fixed-layout)",{"children":["email-verification",{"children":["__PAGE__?{\"email\":\"<EMAIL>\",\"pending_authentication_token\":\"BL3KSONR7cLZRUokJILBAXRsE\",\"redirect_uri\":\"https://cursor.com/api/auth/callback\"}",{}]}]}]}]},"$undefined","$undefined",true],["",{"children":["(root)",{"children":["(fixed-layout)",{"children":["email-verification",{"children":["__PAGE__",{},[["$L1","$L2",null],null],null]},[null,["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","(root)","children","(fixed-layout)","children","email-verification","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]],null]},[[null,["$","div",null,{"style":{"--grid-template-columns":"minmax(0, 440px)","--grid-template-rows":"repeat(3, min-content)","--min-width":"320px","--min-height":"100dvh","alignContent":"space-between"},"children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","(root)","children","(fixed-layout)","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}],"className":"rt-Grid rt-r-gtc rt-r-gtr rt-r-ai-center rt-r-jc-center rt-r-p-4 rt-r-min-w rt-r-min-h"}]],null],null]},[[null,"$L5"],null],null]},[[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/dedb41b2cee06beb.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/9c76d98f250ad12b.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","2",{"rel":"stylesheet","href":"/_next/static/css/09146c990b67a71a.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","3",{"rel":"stylesheet","href":"/_next/static/css/08e69fc4b8a7f5eb.css","precedence":"next","crossOrigin":"$undefined"}],["$","link","4",{"rel":"stylesheet","href":"/_next/static/css/fd6e9161ba6bcfa5.css","precedence":"next","crossOrigin":"$undefined"}]],"$L6"],null],null],["$L7",null]]]]
8:I[83862,["5211","static/chunks/a9fa95c6-4049caa31ccc4f04.js","1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8198","static/chunks/8198-540e8f3556b8489b.js","7601","static/chunks/app/error-885a174101de545e.js"],"default"]
9:I[59851,["1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","8198","static/chunks/8198-540e8f3556b8489b.js","1077","static/chunks/1077-e78d69004c34c144.js","9786","static/chunks/app/(root)/(fixed-layout)/email-verification/page-efdbc1ea8de29f9f.js"],"Theme"]
a:I[88198,["1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","8198","static/chunks/8198-540e8f3556b8489b.js","1077","static/chunks/1077-e78d69004c34c144.js","9786","static/chunks/app/(root)/(fixed-layout)/email-verification/page-efdbc1ea8de29f9f.js"],""]
6:["$","html",null,{"suppressHydrationWarning":true,"className":"__variable_6d6793","lang":"en","children":["$","body",null,{"children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"error":"$8","errorStyles":[],"errorScripts":[],"template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$L9",null,{"asChild":true,"accentColor":"gray","grayColor":"gray","children":["$","div",null,{"style":{"--grid-template-columns":"minmax(0, 440px)","--grid-template-rows":"repeat(3, min-content)","--min-width":"320px","--min-height":"100dvh","alignContent":"space-between"},"children":["$","div",null,{"style":{"gridRowStart":2},"children":[["$","h1",null,{"children":"Page not found","data-accent-color":"$undefined","style":"$undefined","className":"rt-Heading rt-r-size-6 rt-r-ta-center"}],["$","div",null,{"children":["$","p",null,{"children":["This page doesn't exist. Return to"," ",["$","$La",null,{"href":"/","children":"sign-in","data-accent-color":"$undefined","style":"$undefined","className":"rt-Text BrandedLink rt-reset"}],"."],"data-accent-color":"$undefined","style":"$undefined","className":"rt-Text rt-r-size-3 rt-r-ta-center"}],"style":"$undefined","className":"rt-reset rt-BaseCard rt-Card rt-r-size-4 xs:rt-r-size-5 rt-variant-surface"}]],"className":"rt-Flex rt-r-fd-column rt-r-gap-5 rt-r-pt-4 rt-r-pb-9"}],"className":"rt-Grid rt-r-gtc rt-r-gtr rt-r-ai-center rt-r-jc-center rt-r-p-4 rt-r-min-w rt-r-min-h"}]}],"notFoundStyles":[]}]}]}]
7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"Verify your email"}],["$","meta","3",{"name":"next-size-adjust"}]]
1:null
c:I[95809,["1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","8198","static/chunks/8198-540e8f3556b8489b.js","1077","static/chunks/1077-e78d69004c34c144.js","9786","static/chunks/app/(root)/(fixed-layout)/email-verification/page-efdbc1ea8de29f9f.js"],"OtpForm"]
f:I[35042,["1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","8198","static/chunks/8198-540e8f3556b8489b.js","1077","static/chunks/1077-e78d69004c34c144.js","9786","static/chunks/app/(root)/(fixed-layout)/email-verification/page-efdbc1ea8de29f9f.js"],"Favicon"]
10:I[22934,["7996","static/chunks/907791fa-f8dcd1f9d493a273.js","1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","7362","static/chunks/7362-56ede35a3684a18b.js","4841","static/chunks/4841-a53c3128e7985675.js","8332","static/chunks/app/(root)/layout-e26ba5f970eb014c.js"],"PageTracker"]
11:I[63031,["7996","static/chunks/907791fa-f8dcd1f9d493a273.js","1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","7362","static/chunks/7362-56ede35a3684a18b.js","4841","static/chunks/4841-a53c3128e7985675.js","8332","static/chunks/app/(root)/layout-e26ba5f970eb014c.js"],"Fingerprint"]
12:I[1765,["7996","static/chunks/907791fa-f8dcd1f9d493a273.js","1886","static/chunks/1886-5086c823628622c6.js","3718","static/chunks/3718-6b3720d25503e3db.js","8971","static/chunks/8971-e33ce398cf990599.js","7362","static/chunks/7362-56ede35a3684a18b.js","4841","static/chunks/4841-a53c3128e7985675.js","8332","static/chunks/app/(root)/layout-e26ba5f970eb014c.js"],"ThemeProvider"]
d:{"id":"e75011da58d295bef5aa55740d0758a006468655","bound":null}
2:["$","div",null,{"style":{"gridRowStart":2},"children":["$Lb",["$","div",null,{"children":[["$","p",null,{"children":["Enter the code sent to ",["$","span",null,{"children":"<EMAIL>","data-accent-color":"$undefined","style":"$undefined","className":"rt-Text rt-r-weight-bold"}]],"data-accent-color":"$undefined","style":"$undefined","className":"rt-Text rt-r-size-3 rt-r-lt-start rt-r-mb-4"}],["$","$Lc",null,{"authorizationSessionId":"$undefined","redirectUri":"https://cursor.com/api/auth/callback","state":"$undefined","verifyAction":"$Fd","extraHiddenFormFields":{"email":"<EMAIL>","organization_id":"$undefined","pending_authentication_token":"BL3KSONR7cLZRUokJILBAXRsE"}}]],"style":"$undefined","className":"rt-reset rt-BaseCard rt-Card rt-r-size-4 xs:rt-r-size-5 rt-variant-surface"}],["$","div",null,{"children":["$","$La",null,{"href":"/?email=xjleoefor5az%40fashionmusic.asia&redirect_uri=https%3A%2F%2Fcursor.com%2Fapi%2Fauth%2Fcallback","children":[["$","svg",null,{"fill":"currentcolor","height":"15","viewBox":"0 0 15 15","width":"15","xmlns":"http://www.w3.org/2000/svg","children":["$","path",null,{"clipRule":"evenodd","d":"M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z","fillRule":"evenodd"}]}],"Back to sign-in"],"data-accent-color":"gray","style":"$undefined","className":"rt-Text rt-reset rt-Link rt-r-size-2 rt-underline-auto rt-Flex rt-r-ai-center rt-r-gap-1 -rt-r-ml-2"}],"style":"$undefined","className":"rt-Flex rt-r-jc-center"}]],"className":"rt-Flex rt-r-fd-column rt-r-gap-5 rt-r-pt-4 rt-r-pb-9"}]
e:T2b8d,
              

.radix-themes {
  --cursor-button: pointer;
  --cursor-disabled: default;
  --cursor-link: pointer;
  --cursor-menu-item: pointer;
  --cursor-slider-thumb: pointer;
  --cursor-slider-thumb-active: pointer;
  --cursor-switch: pointer;

  --system-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI (Custom)', Roboto, 'Helvetica Neue', 'Open Sans (Custom)', system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
  --default-font-family: Inter, var(--system-font-family);
  --heading-font-family: InterDisplay, var(--system-font-family);
  --font-weight-bold: 600;

  --code-padding-top: 0em;
  --code-padding-bottom: 0.075em;
  --code-font-family: 'IBM Plex Mono', 'Menlo', monospace, 'Apple Color Emoji',
    'Segoe UI Emoji';

  --marker-font-size-adjust: 0.97222;
  --marker-font-weight: 500;

  font-feature-settings: 'liga' 1, 'calt' 1;
}

@supports (font-variation-settings: normal) {
  .radix-themes {
    --default-font-family: var(--inter-variable-font, InterVariable), var(--system-font-family);
    --heading-font-family: var(--inter-variable-font, InterVariable), var(--system-font-family);
  }
}


:root, .light, .light-theme {
  --branded-link-color: #5753C6;
  --branded-link-text-decoration-color: #5753c666;
  --branded-button-color: #FFFFFF;
  --branded-button-background: #000000;
  --branded-button-hover-background: #2e2e2e;
  --branded-button-active-background: #414141;
  --branded-button-box-shadow-color: transparent;
  --branded-page-background: #FCFCFC;
}

.dark, .dark-theme {
  --branded-link-color: #B1A9FF;
  --branded-link-text-decoration-color: #b1a9ff66;
  --branded-button-color: #000000;
  --branded-button-background: #FFFFFF;
  --branded-button-hover-background: #f6f6f6;
  --branded-button-active-background: #ededed;
  --branded-button-box-shadow-color: transparent;
  --branded-page-background: #000000;
}

.radix-themes:not(.dark, .dark-theme), .radix-themes:is(.light, .light-theme) {
  --accent-1: #fcfcfc;
  --accent-2: #f9f9f9;
  --accent-3: #f0f0f0;
  --accent-4: #e8e8e8;
  --accent-5: #e0e0e0;
  --accent-6: #d9d9d9;
  --accent-7: #cecece;
  --accent-8: #bbb;
  --accent-9: #000;
  --accent-10: #2e2e2e;
  --accent-11: #646464;
  --accent-12: #202020;

  --accent-a1: #00000003;
  --accent-a2: #00000006;
  --accent-a3: #0000000f;
  --accent-a4: #00000017;
  --accent-a5: #0000001f;
  --accent-a6: #00000026;
  --accent-a7: #00000031;
  --accent-a8: #00000044;
  --accent-a9: #000000;
  --accent-a10: #000000d1;
  --accent-a11: #0000009b;
  --accent-a12: #000000df;

  --gray-1: #fcfcfc;
  --gray-2: #f9f9f9;
  --gray-3: #f0f0f0;
  --gray-4: #e8e8e8;
  --gray-5: #e0e0e0;
  --gray-6: #d9d9d9;
  --gray-7: #cecece;
  --gray-8: #bbb;
  --gray-9: #8d8d8d;
  --gray-10: #838383;
  --gray-11: #646464;
  --gray-12: #202020;

  --gray-a1: #00000003;
  --gray-a2: #00000006;
  --gray-a3: #0000000f;
  --gray-a4: #00000017;
  --gray-a5: #0000001f;
  --gray-a6: #00000026;
  --gray-a7: #00000031;
  --gray-a8: #00000044;
  --gray-a9: #00000072;
  --gray-a10: #0000007c;
  --gray-a11: #0000009b;
  --gray-a12: #000000df;

  --accent-track: var(--accent-9);
  --accent-indicator: var(--accent-9);
  --accent-contrast: #fff;
  --gray-surface: #ffffffcc;
}

.radix-themes:not(.dark, .dark-theme), .radix-themes:is(.light, .light-theme), :where(.radix-themes:not(.dark, .dark-theme), .radix-themes:is(.light, .light-theme)) [data-accent-color] {
  --focus-1: #fdfdff;
  --focus-2: #f7f8ff;
  --focus-3: #eff1fe;
  --focus-4: #e4e8ff;
  --focus-5: #d8ddff;
  --focus-6: #c8ceff;
  --focus-7: #b5bbf9;
  --focus-8: #999ff0;
  --focus-9: #5e5ad5;
  --focus-10: #564fcc;
  --focus-11: #5753c6;
  --focus-12: #292862;

  --focus-a1: #0000ff02;
  --focus-a2: #0020ff08;
  --focus-a3: #0020f010;
  --focus-a4: #0026ff1b;
  --focus-a5: #0021ff27;
  --focus-a6: #001cff37;
  --focus-a7: #0115eb4a;
  --focus-a8: #000fda66;
  --focus-a9: #0600bea5;
  --focus-a10: #0a00b5b0;
  --focus-a11: #0600abac;
  --focus-a12: #010045d7;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .radix-themes:not(.dark, .dark-theme),
    .radix-themes:is(.light, .light-theme) {
      --accent-a1: color(display-p3 0 0 0 / 0.0118);
      --accent-a2: color(display-p3 0 0 0 / 0.0235);
      --accent-a3: color(display-p3 0 0 0 / 0.0588);
      --accent-a4: color(display-p3 0 0 0 / 0.0902);
      --accent-a5: color(display-p3 0 0 0 / 0.1216);
      --accent-a6: color(display-p3 0 0 0 / 0.149);
      --accent-a7: color(display-p3 0 0 0 / 0.1922);
      --accent-a8: color(display-p3 0 0 0 / 0.2667);
      --accent-a9: color(display-p3 0 0 0);
      --accent-a10: color(display-p3 0 0 0 / 0.8196);
      --accent-a11: color(display-p3 0 0 0 / 0.6078);
      --accent-a12: color(display-p3 0 0 0 / 0.8745);

      --gray-a1: color(display-p3 0 0 0 / 0.0118);
      --gray-a2: color(display-p3 0 0 0 / 0.0235);
      --gray-a3: color(display-p3 0 0 0 / 0.0588);
      --gray-a4: color(display-p3 0 0 0 / 0.0902);
      --gray-a5: color(display-p3 0 0 0 / 0.1216);
      --gray-a6: color(display-p3 0 0 0 / 0.149);
      --gray-a7: color(display-p3 0 0 0 / 0.1922);
      --gray-a8: color(display-p3 0 0 0 / 0.2667);
      --gray-a9: color(display-p3 0 0 0 / 0.4471);
      --gray-a10: color(display-p3 0 0 0 / 0.4863);
      --gray-a11: color(display-p3 0 0 0 / 0.6078);
      --gray-a12: color(display-p3 0 0 0 / 0.8745);
    }

    .radix-themes:not(.dark, .dark-theme), .radix-themes:is(.light, .light-theme), :where(.radix-themes:not(.dark, .dark-theme), .radix-themes:is(.light, .light-theme)) [data-accent-color] {
      --focus-a1: color(display-p3 0.0196 0.0196 1 / 0.008);
      --focus-a2: color(display-p3 0.0196 0.1451 0.8784 / 0.032);
      --focus-a3: color(display-p3 0.0078 0.1294 0.8784 / 0.063);
      --focus-a4: color(display-p3 0.0039 0.1176 0.9255 / 0.102);
      --focus-a5: color(display-p3 0.0078 0.1137 0.9216 / 0.15);
      --focus-a6: color(display-p3 0.0039 0.0941 0.9294 / 0.212);
      --focus-a7: color(display-p3 0.0039 0.0745 0.851 / 0.287);
      --focus-a8: color(display-p3 0.0039 0.051 0.7922 / 0.397);
      --focus-a9: color(display-p3 0.0196 0 0.702 / 0.648);
      --focus-a10: color(display-p3 0.0353 0 0.6706 / 0.691);
      --focus-a11: color(display-p3 0.0196 0 0.6275 / 0.675);
      --focus-a12: color(display-p3 0.0039 0 0.251 / 0.844);
    }
  }
}

:is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme) {
  --accent-1: #111;
  --accent-2: #191919;
  --accent-3: #232323;
  --accent-4: #2a2a2a;
  --accent-5: #313131;
  --accent-6: #3a3a3a;
  --accent-7: #484848;
  --accent-8: #616161;
  --accent-9: #fff;
  --accent-10: #f6f6f6;
  --accent-11: #b4b4b4;
  --accent-12: #eee;

  --accent-a1: #ffffff11;
  --accent-a2: #ffffff19;
  --accent-a3: #ffffff23;
  --accent-a4: #ffffff2a;
  --accent-a5: #ffffff31;
  --accent-a6: #ffffff3a;
  --accent-a7: #ffffff48;
  --accent-a8: #ffffff61;
  --accent-a9: #ffffff;
  --accent-a10: #fffffff6;
  --accent-a11: #ffffffb4;
  --accent-a12: #ffffffee;

  --gray-1: #000;
  --gray-2: #191919;
  --gray-3: #191919;
  --gray-4: #191919;
  --gray-5: #1e1e1e;
  --gray-6: #242424;
  --gray-7: #2e2e2e;
  --gray-8: #3f3f3f;
  --gray-9: #494949;
  --gray-10: #5d5d5d;
  --gray-11: #9e9e9e;
  --gray-12: #eee;

  --gray-a1: #00000000;
  --gray-a2: #ffffff19;
  --gray-a3: #ffffff19;
  --gray-a4: #ffffff19;
  --gray-a5: #ffffff1e;
  --gray-a6: #ffffff24;
  --gray-a7: #ffffff2e;
  --gray-a8: #ffffff3f;
  --gray-a9: #ffffff49;
  --gray-a10: #ffffff5d;
  --gray-a11: #ffffff9e;
  --gray-a12: #ffffffee;

  --accent-track: var(--accent-9);
  --accent-indicator: var(--accent-9);
  --accent-contrast: #000;
  --gray-surface: rgba(0, 0, 0, 0.05);
}

.radix-themes:is(.dark, .dark-theme), :is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme), :where(.radix-themes:is(.dark, .dark-theme), :is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme)) [data-accent-color] {
  --focus-1: #14131e;
  --focus-2: #171625;
  --focus-3: #262047;
  --focus-4: #312662;
  --focus-5: #3b2f71;
  --focus-6: #463b80;
  --focus-7: #524793;
  --focus-8: #6255ae;
  --focus-9: #6d55d2;
  --focus-10: #7966db;
  --focus-11: #b1a9ff;
  --focus-12: #e0dffe;

  --focus-a1: #aaa2ff1e;
  --focus-a2: #9f98ff25;
  --focus-a3: #8973ff47;
  --focus-a4: #8063ff62;
  --focus-a5: #866bff71;
  --focus-a6: #8c76ff80;
  --focus-a7: #8f7cff93;
  --focus-a8: #907dffae;
  --focus-a9: #8467ffd2;
  --focus-a10: #8d77ffdb;
  --focus-a11: #b1a9ff;
  --focus-a12: #e1e0fffe;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme) {
      --accent-a1: color(display-p3 1 1 1 / 0.0667);
      --accent-a2: color(display-p3 1 1 1 / 0.098);
      --accent-a3: color(display-p3 1 1 1 / 0.1373);
      --accent-a4: color(display-p3 1 1 1 / 0.1647);
      --accent-a5: color(display-p3 1 1 1 / 0.1922);
      --accent-a6: color(display-p3 1 1 1 / 0.2275);
      --accent-a7: color(display-p3 1 1 1 / 0.2824);
      --accent-a8: color(display-p3 1 1 1 / 0.3804);
      --accent-a9: color(display-p3 1 1 1);
      --accent-a10: color(display-p3 1 1 1 / 0.9647);
      --accent-a11: color(display-p3 1 1 1 / 0.7059);
      --accent-a12: color(display-p3 1 1 1 / 0.9333);

      --gray-a1: color(display-p3 0 0 0 / 0);
      --gray-a2: color(display-p3 1 1 1 / 0.098);
      --gray-a3: color(display-p3 1 1 1 / 0.098);
      --gray-a4: color(display-p3 1 1 1 / 0.098);
      --gray-a5: color(display-p3 1 1 1 / 0.1176);
      --gray-a6: color(display-p3 1 1 1 / 0.1412);
      --gray-a7: color(display-p3 1 1 1 / 0.1804);
      --gray-a8: color(display-p3 1 1 1 / 0.2471);
      --gray-a9: color(display-p3 1 1 1 / 0.2863);
      --gray-a10: color(display-p3 1 1 1 / 0.3647);
      --gray-a11: color(display-p3 1 1 1 / 0.6196);
      --gray-a12: color(display-p3 1 1 1 / 0.9333);
    }

    .radix-themes:is(.dark, .dark-theme), :is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme), :where(.radix-themes:is(.dark, .dark-theme), :is(.dark, .dark-theme) .radix-themes:not(.light, .light-theme)) [data-accent-color] {
      --focus-a1: color(display-p3 0.6902 0.6549 1 / 0.114);
      --focus-a2: color(display-p3 0.6353 0.6078 0.9961 / 0.142);
      --focus-a3: color(display-p3 0.5373 0.4667 1 / 0.271);
      --focus-a4: color(display-p3 0.502 0.4039 1 / 0.369);
      --focus-a5: color(display-p3 0.5255 0.4314 1 / 0.428);
      --focus-a6: color(display-p3 0.549 0.4784 1 / 0.487);
      --focus-a7: color(display-p3 0.5647 0.502 1 / 0.557);
      --focus-a8: color(display-p3 0.5725 0.5059 1 / 0.659);
      --focus-a9: color(display-p3 0.5176 0.4235 1 / 0.797);
      --focus-a10: color(display-p3 0.5569 0.4863 1 / 0.832);
      --focus-a11: color(display-p3 0.7059 0.6784 1 / 0.977);
      --focus-a12: color(display-p3 0.8902 0.8863 1 / 0.985);
    }
  }
}

              .radix-themes {
                --color-background: var(--branded-page-background);
              }
              :root body {
                margin: 0;
                background-color: var(--branded-page-background);
              }
            5:[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"$e"}}],["$","$Lf",null,{"darkFaviconPath":"$undefined","lightFaviconPath":"$undefined"}],["$","$L10",null,{"children":[["$","$L11",null,{}],["$","$L12",null,{"attribute":"class","forcedTheme":"dark","children":["$","$L9",null,{"radius":"small","children":["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","(root)","children"],"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined"}]}]}]]}]]
13:"$Sreact.fragment"
b:["$","div",null,{"children":[["$","$13","Icon",{"children":[["$","img",null,{"alt":"Logo","src":"https://workos.imgix.net/app-branding/environment_01GS6W3C901N50J4ZGFB6V1Z6C/01HZ3C68S32DEFKTDAYKX864DE","className":"ak-Logo ak-Logo-light ak-LogoIcon"}],["$","img",null,{"alt":"Logo","src":"https://workos.imgix.net/app-branding/environment_01GS6W3C901N50J4ZGFB6V1Z6C/01HZ3C68S3T4CVGPHJPA5JQ19B","className":"ak-Logo ak-Logo-dark ak-LogoIcon"}]]}],["$","h1",null,{"children":"Verify your email","data-accent-color":"$undefined","style":"$undefined","className":"rt-Heading rt-r-size-6 rt-r-ta-center rt-r-tw-balance HeaderHeading"}]],"style":"$undefined","className":"rt-Flex rt-r-fd-column rt-r-ai-center rt-r-jc-center rt-r-gap-4"}]


接口执行耗时: 1.92秒

成功提取token: BL3KSONR7cLZRUokJILBAXRsE

等待邮件到达...

===== 开始执行：获取临时邮箱邮件列表 =====
使用邮箱地址: <EMAIL>
编码后的邮箱: cleverBoy%40mailto.plus
请求URL: /api/mails?email=cleverBoy%40mailto.plus&first_id=0&epin=

响应状态码: 200
原始响应内容: {"count":10,"first_id":**********,"last_id":3398305800,"limit":10,"mail_list":[{"attachment_count":0,"first_attachment_name":"","<EMAIL>","from_name":"","is_new":true,"mail_id":**********,"subject":"","time":"2025-06-17 13:40:15"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3401973588,"subject":"","time":"2025-06-17 08:17:29"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3401927217,"subject":"","time":"2025-06-17 08:07:42"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3401915560,"subject":"","time":"2025-06-17 08:05:10"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3401899651,"subject":"","time":"2025-06-17 08:01:56"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3401859205,"subject":"","time":"2025-06-17 07:52:17"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3398542161,"subject":"","time":"2025-06-16 18:31:53"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3398521716,"subject":"","time":"2025-06-16 18:26:55"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3398434667,"subject":"","time":"2025-06-16 18:05:03"},{"attachment_count":0,"first_attachment_name":"","from_mail":"<EMAIL>","from_name":"","is_new":false,"mail_id":3398305800,"subject":"","time":"2025-06-16 17:41:48"}],"more":true,"result":true}

解析后的响应数据:
{
  "count": 10,
  "first_id": **********,
  "last_id": 3398305800,
  "limit": 10,
  "mail_list": [
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": true,
      "mail_id": **********,
      "subject": "",
      "time": "2025-06-17 13:40:15"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3401973588,
      "subject": "",
      "time": "2025-06-17 08:17:29"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3401927217,
      "subject": "",
      "time": "2025-06-17 08:07:42"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3401915560,
      "subject": "",
      "time": "2025-06-17 08:05:10"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3401899651,
      "subject": "",
      "time": "2025-06-17 08:01:56"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3401859205,
      "subject": "",
      "time": "2025-06-17 07:52:17"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3398542161,
      "subject": "",
      "time": "2025-06-16 18:31:53"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3398521716,
      "subject": "",
      "time": "2025-06-16 18:26:55"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3398434667,
      "subject": "",
      "time": "2025-06-16 18:05:03"
    },
    {
      "attachment_count": 0,
      "first_attachment_name": "",
      "from_mail": "<EMAIL>",
      "from_name": "",
      "is_new": false,
      "mail_id": 3398305800,
      "subject": "",
      "time": "2025-06-16 17:41:48"
    }
  ],
  "more": true,
  "result": true
}

接口执行耗时: 0.81秒
获取到第一封邮件ID: **********
===== 结束执行：获取临时邮箱邮件列表 =====


===== 开始执行：获取邮件内容 =====
使用邮箱地址: <EMAIL>
编码后的邮箱: cleverBoy%40mailto.plus
获取邮件ID: **********
请求URL: /api/mails/**********?email=cleverBoy%40mailto.plus&epin=

响应状态码: 200
获取邮件内容响应:
{
  "attachments": [],
  "date": "Tue, 17 Jun 2025 10:40:12 +0000 (UTC)",
  "from": "Cursor <<EMAIL>>",
  "from_is_local": false,
  "from_mail": "<EMAIL>",
  "from_name": "Cursor",
  "html": "<!doctype html><html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\"><head><title></title><!--[if !mso]><!--><meta http-e...",
  "is_tls": true,
  "mail_id": **********,
  "message_id": "<f_h8QiLfSYGlTJmrrymo0Q@geopod-ismtpd-26>",
  "result": true,
  "subject": "Verify your email address",
  "text": "Verify your email\n\nYou need to verify your <NAME_EMAIL> before you can access\nyour account. Enter the code below in your open browser window.\n\n207877\n\nThis code expires in 10 minutes.\n\n\n---\n\nIf you didn\u2019t request this code, you can safely ignore this email.\nSomeone else might have typed your email address by mistake.",
  "to": "<EMAIL>"
}
接口执行耗时: 1.18秒
提取的验证码: 207877
===== 结束执行：获取邮件内容 =====


===== 开始执行：提交邮箱验证码 =====
邮箱: <EMAIL>
验证Token: BL3KSONR7cLZRUokJILBAXRsE
验证码: 207877
使用boundary: 6fb854e99c994debb5fccfe529fb030b

请求体片段:
--6fb854e99c994debb5fccfe529fb030b
Content-Disposition: form-data; name=1_pending_authentication_token
Content-Type: text/plain

BL3KSONR7cLZRUokJILBAXRsE
--6fb854e99c994debb5fccfe529fb030b
Content-Disposition: form-data; name=1_email
Content-Type: text/plain

<EMAIL>
--6fb854e99c994debb5fccfe529fb030b
Content-Disposition: form-data; name=1_redirect_uri
Content-Type: text/plain

https://cursor.com/api/auth/callback
--6fb854e99c994debb5fccfe529fb030b
Content...
请求URL: /email-verification?email=xjleoefor5az%40fashionmusic.asia&pending_authentication_token=BL3KSONR7cLZRUokJILBAXRsE&redirect_uri=https://cursor.c/api/auth/callback

从响应中获取的Cookie: __cf_bm=4YLVseZr4bYaNYSbOaNpuGb6NBjnbJINLyKH1GsNYWk-1750156825-*******-UPhlYoe008ntxkgplYOJSui5QBhmmOB8w3Kw7oUF1R600TLXjUPvM5sgVFUS.yvutaNo03SVOfjgj3Z130HcIWvkradb6DoqifvY; _cfuvid=74AoQVHDB7iLQP72Hlwk8G0DuZ_hyydBUuz9aplO7ks-1750156825531-*******-604800000

响应状态码: 303
验证码提交响应:
{}
接口执行耗时: 0.83秒

验证成功，开始生成PKCE对并轮询认证状态...
生成的PKCE验证对:
code_verifier: r2eu7FaGpD070QRID1jAAxTFKUcPAaFfIjMIuUNtrZ5PpaaRcy61urJe1w
code_challenge: 51ZesSl-QSBJmssY8zquZkJALuO6jC67Kw4lN4Hwbf4
使用UUID: ca0e6f07-388e-4b68-909c-a3b88b1fa29c

===== 开始执行：登录深度回调控制 =====
请求URL: /loginDeepControl?challenge=51ZesSl-QSBJmssY8zquZkJALuO6jC67Kw4lN4Hwbf4&uuid=ca0e6f07-388e-4b68-909c-a3b88b1fa29c&model=login
使用Cookie: __cf_bm=4YLVseZr4bYaNYSbOaNpuGb6NBjnbJINLyKH1GsNYWk-1750156825-*******-UPhlYoe008ntxkgplYOJSui5QBhmmOB8w3Kw7oUF1R600TLXjUPvM5sgVFUfM7yMKDS.ytaNo03SVOfjgj3Z130HcIWvkradb6DoqifvY; _cfuvid=74AoQVHDB7iLQP72Hlwk8G0DuZ_hyydBUuz9aplO7ks-1750156825531-*******-604800000
请求参数:
UUID: ca0e6f07-388e-4b68-909c-a3b88b1fa29c
Challenge: 51ZesSl-QSBJmssY8zquZkJALuO6jC67Kw4lN4Hwbf4

响应状态码: 307
响应原因: Temporary Redirect

===== 响应头信息 =====
Accept-Ch: Sec-CH-UA-Arch, Sec-CH-UA-Platform, Sec-CH-UA-Platform-Version, Sec-CH-UA-Bitness
Cache-Control: public, max-age=0, must-revalidate
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.vercel.live https://vercel.live https://*.vercel-scripts.com https://*.posthog.com https://*.unifyintent.com; style-src 'self' 'unsafe-inline' https://vercel.com; img-src 'self' data: https://vercel.com https://*.basehub.earth https://*.basehub.com https://*.workoscdn.com https://*.vercel.live https://vercel.com https://*.vercel-scripts.com https://*.pusher.com wss://*.pusher.com https://*.posthog.com https://*.cursor.sh https://*.cursor.com https://*.unifyintent.com; font-src 'self' data: https://vercel.live; connect-src 'self' https://*.basehub.earth https://*.basehub.com https://*.workoscdn.com https://*.vercel.live https://vercel.com https://*.vercel-scripts.com https://*.pusher.com wss://*.pusher.com https://*.posthog.com https://*.cursor.sh https://*.cursor.com https://*.unifyintent.com https://vercel.live https://*.pusher.com wss://*.pusher.com http://localhost:* http://127.0.0.1:* ws://localhost:* ws://127.0.0.1:* wss://localhost:* wss://127.0.0.1:* https://*.posthog.com https://unifyintent.com; media-src 'self' https://*.basehub.earth https://*.basehub.com https://*.workoscdn.com https://*.vercel.live https://vercel.com https://*.vercel-scripts.com https://*.pusher.com wss://*.pusher.com https://*.posthog.com https://*.cursor.sh https://*.cursor.com https://*.unifyintent.com; frame-src https://vercel.live
Content-Type: text/plain
Date: Tue, 17 Jun 2025 10:40:25 GMT
Location: /en/loginDeepControl?challenge=51ZesSl-QSBJmssY8zquZkJALuO6jC67Kw4lN4Hwbf4&uuid=ca0e6f07-388e-4b68-909c-a3b88b1fa29c&model=login
Referrer-Policy: strict-origin-when-cross-origin
Server: Vercel
Set-Cookie: NEXT_LOCALE=en; Path=/; Expires=Wed, 17 Jun 2026 10:40:25 GMT; Max-Age=31536000; Secure; SameSite=lax
Strict-Transport-Security: max-age=31536000; includeSubDomains
X-Content-Type-Options: nosniff
X-Frame-Options: SAMEORIGIN
X-Permitted-Cross-Domain-Policies: none
X-Vercel-Id: sin1::7l4br-1750156825786-2eac7bba6e88
X-Xss-Protection: 1; mode=block
Transfer-Encoding: chunked

===== Set-Cookie 头部信息 =====
Cookie 1: NEXT_LOCALE=en; Path=/; Expires=Wed, 17 Jun 2026 10:40:25 GMT; Max-Age=31536000; Secure; SameSite=lax
==================================================
响应内容:
Redirecting...


接口执行耗时: 0.27秒
===== 结束执行：登录深度回调控制 =====

开始轮询认证状态...

===== 开始执行：轮询认证状态 =====
请求URL: /auth/poll?uuid=ca0e6f07-388e-4b68-909c-a3b88b1fa29c&verifier=r2eu7FaGpD070QRID1jAAxTFKUcPAaFfIjMIuUNtrZ5PpaaRcy61urJe1w

响应状态码: 404
响应内容:
Not found

接口执行耗时: 1.14秒
===== 结束执行：轮询认证状态 =====

===== 结束执行：提交邮箱验证码 =====


========== 完整流程执行结果 ==========
{
  "user_info": {
    "first_name": "ASbOk",
    "last_name": "bzanFsvp",
    "email": "<EMAIL>",
    "password": "XGDhBbnI0w"
  },
  "pending_token": "BL3KSONR7cLZRUokJILBAXRsE",
  "verification_code": "207877",
  "verification_result": "\u6210\u529f\u63d0\u4ea4\u9a8c\u8bc1\u7801",
  "pkce_info": {
    "verifier": "r2eu7FaGpD070QRID1jAAxTFKUcPAaFfIjMIuUNtrZ5PpaaRcy61urJe1w",
    "challenge": "51ZesSl-QSBJmssY8zquZkJALuO6jC67Kw4lN4Hwbf4",
    "uuid": "ca0e6f07-388e-4b68-909c-a3b88b1fa29c",
    "poll_response": "Not found"
  }
}
总执行耗时: 28.32秒
========== 完整注册流程结束 ==========

(venv) flx@fanlixing-Mac cursor-free % 










