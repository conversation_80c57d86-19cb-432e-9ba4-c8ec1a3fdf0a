# AI运营后台付费管理系统 - 技术方案（飞书版）

## 1. 项目概述

### 1.1 产品背景
AI运营后台二期·V2.0 付费管理系统，主要解决套餐管理、订单支付、账单管理、发票开具等核心业务需求。

### 1.2 技术栈选型

```
┌─────────────────────────────────────────────────────────────┐
│                        技术栈架构                            │
├─────────────────────────────────────────────────────────────┤
│  前端层    │  Vue 3.x + Element Plus + TypeScript          │
├─────────────────────────────────────────────────────────────┤
│  网关层    │  Spring Cloud Gateway + Nacos                │
├─────────────────────────────────────────────────────────────┤
│  服务层    │  Spring Boot 3.2 + Spring Security 6.x       │
├─────────────────────────────────────────────────────────────┤
│  数据层    │  MySQL 8.0 + Redis 7.x + MyBatis-Plus       │
├─────────────────────────────────────────────────────────────┤
│  消息层    │  RocketMQ 5.x + WebSocket                    │
├─────────────────────────────────────────────────────────────┤
│  基础设施  │  Docker + K8s + Jenkins                      │
└─────────────────────────────────────────────────────────────┘
```

## 2. 系统架构设计

### 2.1 整体架构图

```
                    ┌─────────────────────────────────────┐
                    │            前端应用层                │
                    │  ┌─────────────┐  ┌─────────────┐   │
                    │  │ 运营后台    │  │ 门店管理    │   │
                    │  │   管理      │  │   平台      │   │
                    │  └─────────────┘  └─────────────┘   │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────▼───────────────────────┐
                    │           API网关层                  │
                    │     Spring Cloud Gateway            │
                    └─────────────┬───────────────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
        ▼                         ▼                         ▼
┌─────────────┐         ┌─────────────┐         ┌─────────────┐
│ 套餐管理服务 │         │ 订单管理服务 │         │ 支付管理服务 │
│             │         │             │         │             │
│ - 套餐CRUD  │         │ - 订单创建  │         │ - 支付接口  │
│ - 产品管理  │         │ - 状态流转  │         │ - 账单管理  │
│ - 定价管理  │         │ - 订单查询  │         │ - 退款处理  │
└─────────────┘         └─────────────┘         └─────────────┘
        │                         │                         │
        └─────────────────────────┼─────────────────────────┘
                                  │
                    ┌─────────────▼───────────────────────┐
                    │          基础服务层                  │
                    │ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │ │用户服务 │ │通知服务 │ │文件服务 │ │
                    │ └─────────┘ └─────────┘ └─────────┘ │
                    └─────────────┬───────────────────────┘
                                  │
                    ┌─────────────▼───────────────────────┐
                    │            数据存储层                │
                    │ ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │ │MySQL主库│ │Redis缓存│ │MongoDB  │ │
                    │ │MySQL从库│ │         │ │日志存储 │ │
                    │ └─────────┘ └─────────┘ └─────────┘ │
                    └─────────────────────────────────────┘
```

### 2.2 微服务拆分策略

```
核心业务服务：
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  套餐管理服务    │    │  订单管理服务    │    │  支付管理服务    │
│                │    │                │    │                │
│ • 套餐CRUD     │    │ • 订单创建     │    │ • 微信支付     │
│ • 产品管理     │    │ • 状态流转     │    │ • 支付宝      │
│ • 定价策略     │    │ • 订单统计     │    │ • 账单管理     │
│ • 门店配置     │    │ • 超时处理     │    │ • 退款处理     │
└─────────────────┘    └─────────────────┘    └─────────────────┘

基础支撑服务：
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  发票管理服务    │    │  用户权限服务    │    │  通知服务       │
│                │    │                │    │                │
│ • 发票申请     │    │ • 用户认证     │    │ • 短信通知     │
│ • 审核流程     │    │ • 权限管理     │    │ • 邮件发送     │
│ • 开票推送     │    │ • JWT管理      │    │ • 站内消息     │
│ • 文件管理     │    │ • 多角色支持   │    │ • 推送通知     │
└─────────────────┘    └─────────────────┘    └─────────────────┘

## 3. 核心业务流程设计

### 3.1 套餐创建流程

```
┌─────────────────┐
│  开始创建套餐    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 填写套餐基本信息 │
│ • 套餐名称      │
│ • 付费方式      │
│ • 优惠方式      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  添加产品信息    │
│ • 产品名称      │
    │ • 产品描述      │
│ • 排序权重      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  设置产品定价    │
│ • 月度门市价    │
│ • 季度门市价    │
│ • 年度门市价    │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │ 选择定价模式 │
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│ 折扣模式 │    │一口价模式│
│         │    │         │
│设置折扣 │    │设置固定 │
│比例     │    │价格     │
└────┬────┘    └────┬────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│  计算最终价格    │
│ • 应用优惠策略  │
│ • 计算周期价格  │
│ • 生成价格明细  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  保存套餐信息    │
│ • 保存到数据库  │
│ • 创建产品关联  │
│ • 生成定价记录  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  生成套餐编码    │
│ • 格式：PKG_    │
│   YYYYMMDD_XXXX │
│ • 确保唯一性    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│  设置套餐状态    │
│ • 默认：停用    │
│ • 可选：启用    │
│ • 支持：推荐    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│    完成创建      │
│ • 返回套餐信息  │
│ • 记录操作日志  │
│ • 发送创建通知  │
└─────────────────┘
```

### 3.2 价格计算核心流程

```
┌─────────────────┐
│ 开始价格计算     │
│ • 套餐ID        │
│ • 购买周期      │
│ • 门店编码      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 获取套餐信息     │
│ • 套餐基本信息  │
│ • 优惠模式      │
│ • 产品列表      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 遍历产品列表     │
│ • 获取产品定价  │
│ • 检查个性化配置│
│ • 计算单品价格  │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │套餐优惠模式│
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│ 折扣模式 │    │一口价模式│
│         │    │         │
│门市价 × │    │直接使用 │
│(1-折扣率)│    │一口价   │
└────┬────┘    └────┬────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│ 检查个性化定价   │
│ • 查询门店配置  │
│ • 判断是否覆盖  │
│ • 应用自定义价格│
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │是否有个性化│
      │定价配置   │
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│使用门店 │    │使用套餐 │
│自定义价格│    │默认价格 │
└────┬────┘    └────┬────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│ 累加所有产品价格 │
│ • 计算月度总价  │
│ • 记录价格明细  │
│ • 生成计算日志  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 应用购买周期折扣 │
│ • 月度：无折扣  │
│ • 季度：95折    │
│ • 年度：85折    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 计算最终订单金额 │
│ • 原价金额      │
│ • 折扣金额      │
│ • 最终金额      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 返回价格计算结果 │
│ • 价格明细      │
│ • 计算说明      │
│ • 有效期信息    │
└─────────────────┘

### 3.3 订单管理流程

#### 3.3.1 订单创建流程

```
┌─────────────────┐
│ 门店发起购买     │
│ • 选择套餐      │
│ • 选择周期      │
│ • 确认价格      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 验证购买条件     │
│ • 门店状态检查  │
│ • 套餐可用性    │
│ • 权限验证      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 计算订单金额     │
│ • 调用价格计算  │
│ • 应用优惠策略  │
│ • 生成价格明细  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 生成订单编号     │
│ • 格式：ORD_    │
│   YYYYMMDD_XXXX │
│ • 确保唯一性    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 创建订单记录     │
│ • 保存订单信息  │
│ • 设置初始状态  │
│ • 记录创建时间  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 创建订单明细     │
│ • 记录产品信息  │
│ • 保存价格明细  │
│ • 冗余产品名称  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 设置订单过期时间 │
│ • 默认15分钟    │
│ • 可配置时长    │
│ • 启动定时任务  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 返回订单信息     │
│ • 订单编号      │
│ • 支付金额      │
│ • 过期时间      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 跳转支付页面     │
│ • 展示订单详情  │
│ • 选择支付方式  │
│ • 发起支付请求  │
└─────────────────┘
```

#### 3.3.2 订单状态流转

```
                    ┌─────────────┐
                    │   创建订单   │
                    └──────┬──────┘
                           │
                           ▼
                    ┌─────────────┐
                    │  PENDING    │ ◄─── 待支付状态
                    │  (待支付)   │      (15分钟有效期)
                    └──────┬──────┘
                           │
            ┌──────────────┼──────────────┐
            │              │              │
            ▼              ▼              ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │    PAID     │ │ CANCELLED   │ │  EXPIRED    │
    │  (已支付)   │ │  (已取消)   │ │  (已过期)   │
    └─────────────┘ └─────────────┘ └─────────────┘
           │               │              │
           │               │              │
           ▼               ▼              ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │  订单完成    │ │  订单结束   │ │  订单结束   │
    │  开通服务    │ │  释放资源   │ │  清理数据   │
    └─────────────┘ └─────────────┘ └─────────────┘

状态说明：
• PENDING  → PAID      : 支付成功
• PENDING  → CANCELLED : 用户主动取消
• PENDING  → EXPIRED   : 超时自动过期
• PAID     → 不可变更   : 已支付状态不可逆
• CANCELLED→ 不可变更   : 已取消状态不可逆
• EXPIRED  → 不可变更   : 已过期状态不可逆
```

### 3.4 支付管理流程

#### 3.4.1 支付处理流程

```
┌─────────────────┐
│ 用户选择支付方式 │
│ • 微信支付      │
│ • 支付宝支付    │
│ • 其他方式      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 前端发起支付请求 │
│ • 订单编号      │
│ • 支付方式      │
│ • 回调地址      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 网关路由到支付服务│
│ • 验证请求参数  │
│ • 检查订单状态  │
│ • 选择支付适配器│
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │选择支付方式│
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│ 微信支付 │    │ 支付宝  │
│         │    │ 支付    │
│调用微信 │    │调用支付宝│
│支付API  │    │API      │
└────┬────┘    └────┬────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│ 第三方支付平台   │
│ • 生成支付参数  │
│ • 返回支付信息  │
│ • 创建支付订单  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 支付服务处理响应 │
│ • 解析支付参数  │
│ • 生成二维码    │
│ • 设置过期时间  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 返回支付信息     │
│ • 支付链接      │
│ • 二维码图片    │
│ • 过期时间      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 前端展示支付页面 │
│ • 显示二维码    │
│ • 显示金额      │
│ • 轮询支付状态  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 用户完成支付     │
│ • 扫码支付      │
│ • 确认付款      │
│ • 等待结果      │
└─────────────────┘

#### 3.4.2 支付回调处理流程

```
┌─────────────────┐
│ 第三方支付平台   │
│ 发送回调通知     │
│ • 支付结果      │
│ • 交易流水号    │
│ • 签名信息      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 支付服务接收回调 │
│ • 解析回调数据  │
│ • 记录原始请求  │
│ • 开始处理流程  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 验证回调签名     │
│ • 获取签名参数  │
│ • 计算签名值    │
│ • 对比验证结果  │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │ 签名验证   │
      │ 结果      │
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│验证失败 │    │验证成功 │
│         │    │         │
│记录异常 │    │解析回调 │
│日志     │    │数据     │
└────┬────┘    └────┬────┘
     │              │
     │              ▼
     │    ┌─────────────────┐
     │    │ 查询本地订单信息 │
     │    │ • 根据订单号查询│
     │    │ • 检查订单状态  │
     │    │ • 验证金额匹配  │
     │    └─────────┬───────┘
     │              │
     │              ▼
     │          ┌───────────┐
     │          │订单状态检查│
     │          └─────┬─────┘
     │                │
     │        ┌───────┴───────┐
     │        │               │
     │        ▼               ▼
     │   ┌─────────┐    ┌─────────┐
     │   │订单已支付│    │订单待支付│
     │   │         │    │         │
     │   │返回成功 │    │更新订单 │
     │   │响应     │    │状态     │
     │   └─────────┘    └────┬────┘
     │                       │
     │                       ▼
     │             ┌─────────────────┐
     │             │ 创建账单记录     │
     │             │ • 生成账单编号  │
     │             │ • 记录交易信息  │
     │             │ • 保存支付详情  │
     │             └─────────┬───────┘
     │                       │
     │                       ▼
     │             ┌─────────────────┐
     │             │ 发送支付成功消息 │
     │             │ • 订单支付事件  │
     │             │ • 异步消息队列  │
     │             │ • 触发后续流程  │
     │             └─────────┬───────┘
     │                       │
     │                       ▼
     │             ┌─────────────────┐
     │             │ 更新门店服务状态 │
     │             │ • 开通付费服务  │
     │             │ • 设置服务时间  │
     │             │ • 记录服务日志  │
     │             └─────────┬───────┘
     │                       │
     │                       ▼
     │             ┌─────────────────┐
     │             │ 发送用户通知     │
     │             │ • 支付成功短信  │
     │             │ • 邮件通知      │
     │             │ • 站内消息      │
     │             └─────────┬───────┘
     │                       │
     └───────────────────────┼───────────────────────┐
                             │                       │
                             ▼                       ▼
                   ┌─────────────────┐    ┌─────────────────┐
                   │ 返回成功响应     │    │ 返回失败响应     │
                   │ • HTTP 200      │    │ • HTTP 400      │
                   │ • SUCCESS标识   │    │ • ERROR标识     │
                   │ • 处理完成      │    │ • 错误信息      │
                   └─────────────────┘    └─────────────────┘
```

### 3.5 发票管理流程

#### 3.5.1 发票申请流程

```
┌─────────────────┐
│ 门店申请发票     │
│ • 登录系统      │
│ • 进入发票页面  │
│ • 查看可开票账单│
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 选择待开票账单   │
│ • 显示账单列表  │
│ • 筛选未开票    │
│ • 支持多选      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 填写发票信息     │
│ • 发票抬头      │
│ • 纳税人识别号  │
│ • 接收邮箱      │
│ • 发票内容      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 验证发票信息     │
│ • 必填项检查    │
│ • 格式验证      │
│ • 税号校验      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 提交发票申请     │
│ • 生成申请编号  │
│ • 保存申请信息  │
│ • 创建申请记录  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 系统自动审核     │
│ • 基础信息检查  │
│ • 金额合理性    │
│ • 历史记录验证  │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │ 审核结果   │
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│审核通过 │    │审核不通过│
│         │    │         │
│进入开票 │    │返回错误 │
│队列     │    │信息     │
└────┬────┘    └────┬────┘
     │              │
     ▼              ▼
┌─────────────────┐ ┌─────────────────┐
│ 调用开票接口     │ │ 通知申请失败     │
│ • 第三方开票API │ │ • 发送失败通知  │
│ • 传递发票信息  │ │ • 记录失败原因  │
│ • 等待开票结果  │ │ • 支持重新申请  │
└─────────┬───────┘ └─────────────────┘
          │
          ▼
┌─────────────────┐
│ 生成发票文件     │
│ • 下载PDF文件   │
│ • 保存到文件服务│
│ • 生成访问链接  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 发送邮件通知     │
│ • 发票PDF附件   │
│ • 开票完成通知  │
│ • 发票查看链接  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 更新开票状态     │
│ • 更新申请状态  │
│ • 更新账单状态  │
│ • 记录完成时间  │
└─────────────────┘

#### 3.5.2 发票审核工作流

```
                    ┌─────────────┐
                    │ 提交发票申请 │
                    └──────┬──────┘
                           │
                           ▼
                    ┌─────────────┐
                    │  PENDING    │ ◄─── 待审核状态
                    │  (待审核)   │      (系统自动审核)
                    └──────┬──────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ REVIEWING   │ ◄─── 审核中状态
                    │ (审核中)    │      (人工审核)
                    └──────┬──────┘
                           │
            ┌──────────────┼──────────────┐
            │              │              │
            ▼              ▼              ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │  APPROVED   │ │  REJECTED   │ │ INVOICING   │
    │  (已通过)   │ │  (已驳回)   │ │ (开票中)    │
    └──────┬──────┘ └──────┬──────┘ └──────┬──────┘
           │               │               │
           ▼               ▼               ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │ 等待开票     │ │ 申请结束     │ │ 调用开票API │
    └──────┬──────┘ └─────────────┘ └──────┬──────┘
           │                               │
           └───────────────┬───────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │  INVOICED   │ ◄─── 已开票状态
                    │  (已开票)   │      (流程结束)
                    └─────────────┘

状态说明：
• PENDING   → REVIEWING : 进入人工审核
• REVIEWING → APPROVED  : 审核通过
• REVIEWING → REJECTED  : 审核驳回
• APPROVED  → INVOICING : 开始开票
• INVOICING → INVOICED  : 开票完成
• REJECTED  → 流程结束   : 申请失败
• INVOICED  → 流程结束   : 申请成功
```

## 4. 价格计算详细设计

### 4.1 价格计算算法核心

```
价格计算输入参数：
┌─────────────────────────────────────────────────────────────┐
│ • 套餐ID (package_id)                                      │
│ • 购买周期 (purchase_period): MONTHLY/QUARTERLY/YEARLY     │
│ • 门店编码 (hotel_code): 可选，用于个性化定价              │
│ • 房间数量 (room_count): 可选，按房间收费时使用            │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    价格计算核心算法                          │
│                                                            │
│ 1. 获取套餐基本信息                                         │
│    • 套餐优惠模式 (discount_mode)                          │
│    • 付费方式 (payment_mode)                               │
│    • 套餐状态验证                                          │
│                                                            │
│ 2. 获取产品列表和定价                                       │
│    • 查询套餐包含的所有产品                                 │
│    • 获取每个产品的标准定价                                 │
│    • 按购买周期筛选定价信息                                 │
│                                                            │
│ 3. 计算每个产品的价格                                       │
│    FOR EACH 产品 IN 产品列表:                              │
│        IF 套餐优惠模式 == 折扣模式:                        │
│            产品价格 = 门市价 × (1 - 折扣率)                │
│        ELSE IF 套餐优惠模式 == 一口价模式:                 │
│            产品价格 = 一口价                               │
│        END IF                                              │
│                                                            │
│        IF 存在门店个性化定价:                              │
│            IF 自定义优惠价 != NULL:                        │
│                产品价格 = 自定义优惠价                     │
│            ELSE IF 自定义门市价 != NULL:                   │
│                产品价格 = 自定义门市价 × 套餐优惠策略      │
│            ELSE IF 自定义折扣率 != NULL:                   │
│                产品价格 = 门市价 × (1 - 自定义折扣率)      │
│            END IF                                          │
│        END IF                                              │
│                                                            │
│        累加到总价格                                        │
│    END FOR                                                 │
│                                                            │
│ 4. 应用购买周期折扣                                         │
│    IF 购买周期 == MONTHLY:                                 │
│        最终价格 = 月度总价 × 1                             │
│    ELSE IF 购买周期 == QUARTERLY:                          │
│        最终价格 = 月度总价 × 3 × 0.95                      │
│    ELSE IF 购买周期 == YEARLY:                             │
│        最终价格 = 月度总价 × 12 × 0.85                     │
│    END IF                                                  │
│                                                            │
│ 5. 应用房间数量计费（如果适用）                             │
│    IF 付费方式 == 按房间数量收费:                          │
│        最终价格 = 最终价格 × 房间数量                      │
│    END IF                                                  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    价格计算结果输出                          │
│                                                            │
│ • 原始金额 (original_amount): 所有产品月度价格总和         │
│ • 周期折扣金额 (discount_amount): 周期折扣节省的金额       │
│ • 最终金额 (final_amount): 用户实际需要支付的金额          │
│ • 产品价格明细 (product_details): 每个产品的价格详情       │
│ • 计算说明 (calculation_note): 价格计算的详细说明          │
│ • 有效期信息 (validity_info): 服务的有效期时间             │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 个性化定价优先级

```
个性化定价优先级（从高到低）：

┌─────────────────┐
│ 1. 自定义优惠价  │ ◄─── 最高优先级
│ (custom_discount │      直接使用，不再计算
│  _price)        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. 自定义门市价  │ ◄─── 第二优先级
│ (custom_market  │      重新应用套餐优惠策略
│  _price)        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. 自定义折扣率  │ ◄─── 第三优先级
│ (custom_discount│      使用原门市价 × 自定义折扣率
│  _rate)         │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 4. 套餐默认价格  │ ◄─── 最低优先级
│ (default_price) │      使用套餐标准定价策略
└─────────────────┘

计算逻辑示例：
IF 自定义优惠价 IS NOT NULL:
    RETURN 自定义优惠价
ELSE IF 自定义门市价 IS NOT NULL:
    IF 套餐优惠模式 == 折扣模式:
        RETURN 自定义门市价 × (1 - 套餐折扣率)
    ELSE:
        RETURN 自定义门市价  // 一口价模式直接使用
    END IF
ELSE IF 自定义折扣率 IS NOT NULL:
    RETURN 标准门市价 × (1 - 自定义折扣率)
ELSE:
    RETURN 套餐默认计算价格
END IF
```

### 4.3 周期折扣计算规则

```
周期折扣计算详细规则：

月度购买 (MONTHLY)：
┌─────────────────────────────────────────────────────────────┐
│ • 折扣倍数：1.0 (无折扣)                                    │
│ • 计算公式：最终价格 = 月度价格 × 1                         │
│ • 服务时长：1个月                                          │
│ • 适用场景：短期试用、灵活付费                              │
└─────────────────────────────────────────────────────────────┘

季度购买 (QUARTERLY)：
┌─────────────────────────────────────────────────────────────┐
│ • 折扣倍数：2.85 (相当于95折)                               │
│ • 计算公式：最终价格 = 月度价格 × 3 × 0.95                  │
│ • 服务时长：3个月                                          │
│ • 节省金额：月度价格 × 3 × 0.05                            │
│ • 适用场景：中期使用、平衡性价比                            │
└─────────────────────────────────────────────────────────────┘

年度购买 (YEARLY)：
┌─────────────────────────────────────────────────────────────┐
│ • 折扣倍数：10.2 (相当于85折)                               │
│ • 计算公式：最终价格 = 月度价格 × 12 × 0.85                 │
│ • 服务时长：12个月                                         │
│ • 节省金额：月度价格 × 12 × 0.15                           │
│ • 适用场景：长期使用、最大优惠                              │
└─────────────────────────────────────────────────────────────┘

折扣对比示例（假设月度价格1000元）：
┌─────────────────────────────────────────────────────────────┐
│ 购买周期    │ 原价      │ 折扣后价格 │ 节省金额 │ 折扣率    │
│ MONTHLY    │ 1000元    │ 1000元     │ 0元      │ 无折扣    │
│ QUARTERLY  │ 3000元    │ 2850元     │ 150元    │ 5%折扣    │
│ YEARLY     │ 12000元   │ 10200元    │ 1800元   │ 15%折扣   │
└─────────────────────────────────────────────────────────────┘

## 5. 系统集成设计

### 5.1 支付系统集成架构

```
                    ┌─────────────────────────────────────┐
                    │            用户端                   │
                    │ ┌─────────────┐ ┌─────────────┐     │
                    │ │   前端页面   │ │  移动端APP  │     │
                    │ └─────────────┘ └─────────────┘     │
                    └─────────────┬───────────────────────┘
                                  │ HTTPS请求
                                  ▼
                    ┌─────────────────────────────────────┐
                    │          API网关                    │
                    │     • 请求路由                      │
                    │     • 参数验证                      │
                    │     • 安全认证                      │
                    └─────────────┬───────────────────────┘
                                  │
                                  ▼
                    ┌─────────────────────────────────────┐
                    │         支付管理服务                 │
                    │     • 支付订单创建                  │
                    │     • 支付方式选择                  │
                    │     • 回调处理                      │
                    └─────────────┬───────────────────────┘
                                  │
                ┌─────────────────┼─────────────────┐
                │                 │                 │
                ▼                 ▼                 ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │   微信支付适配器 │ │  支付宝适配器   │ │  其他支付适配器 │
    │                │ │                │ │                │
    │ • 统一下单API  │ │ • 创建订单API  │ │ • 银联支付     │
    │ • 查询订单API  │ │ • 查询订单API  │ │ • 其他方式     │
    │ • 申请退款API  │ │ • 申请退款API  │ │ • 扩展接口     │
    └─────────┬───────┘ └─────────┬───────┘ └─────────┬───────┘
              │                   │                   │
              ▼                   ▼                   ▼
    ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
    │    微信支付      │ │     支付宝      │ │    其他平台     │
    │   第三方平台     │ │   第三方平台    │ │   第三方平台    │
    └─────────────────┘ └─────────────────┘ └─────────────────┘
```

## 6. 技术实现要点

### 6.1 核心技术组件

```
Spring Boot 应用架构：
┌─────────────────────────────────────────────────────────────┐
│                    Spring Boot 应用                         │
│                                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                  Web层                                  │ │
│ │ • @RestController - REST接口控制器                      │ │
│ │ • @RequestMapping - 请求路径映射                        │ │
│ │ • @Valid - 参数验证                                     │ │
│ │ • GlobalExceptionHandler - 全局异常处理                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                 Service层                               │ │
│ │ • @Service - 业务逻辑服务                               │ │
│ │ • @Transactional - 事务管理                            │ │
│ │ • @Async - 异步处理                                     │ │
│ │ • @Cacheable - 缓存管理                                 │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                 DAO层                                   │ │
│ │ • MyBatis-Plus BaseMapper                              │ │
│ │ • @Mapper - 数据访问接口                                │ │
│ │ • 自动代码生成                                          │ │
│ │ • 分页插件                                              │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

配置管理：
┌─────────────────────────────────────────────────────────────┐
│ Nacos配置中心：                                             │
│                                                            │
│ • application.yml - 应用基础配置                           │
│ • application-dev.yml - 开发环境配置                       │
│ • application-prod.yml - 生产环境配置                      │
│ • payment-config.yml - 支付相关配置                        │
│ • database-config.yml - 数据库配置                         │
│                                                            │
│ 配置热更新：                                                │
│ • @RefreshScope - 配置刷新                                 │
│ • @ConfigurationProperties - 配置绑定                     │
│ • @Value - 单个配置项                                      │
└─────────────────────────────────────────────────────────────┘
```

### 6.2 安全认证设计

```
JWT认证流程：
┌─────────────────┐
│ 用户登录请求     │
│ • 用户名/密码   │
│ • 验证码        │
│ • 设备信息      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 认证服务验证     │
│ • 密码校验      │
│ • 账户状态检查  │
│ • 权限获取      │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │ 验证结果   │
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│验证失败 │    │验证成功 │
│         │    │         │
│返回错误 │    │生成JWT  │
│信息     │    │Token    │
└─────────┘    └────┬────┘
                    │
                    ▼
          ┌─────────────────┐
          │ JWT Token结构   │
          │                │
          │ Header:        │
          │ • alg: HS256   │
          │ • typ: JWT     │
          │                │
          │ Payload:       │
          │ • sub: 用户ID  │
          │ • iat: 签发时间│
          │ • exp: 过期时间│
          │ • roles: 角色  │
          │ • permissions  │
          │                │
          │ Signature:     │
          │ • 数字签名     │
          └─────────┬───────┘
                    │
                    ▼
          ┌─────────────────┐
          │ 返回认证结果     │
          │ • access_token  │
          │ • refresh_token │
          │ • expires_in    │
          │ • user_info     │
          └─────────────────┘

权限控制：
┌─────────────────────────────────────────────────────────────┐
│ 基于角色的权限控制 (RBAC)：                                 │
│                                                            │
│ 用户 (User) ←→ 角色 (Role) ←→ 权限 (Permission)            │
│                                                            │
│ 角色定义：                                                  │
│ • PAYMENT_ADMIN - 付费管理员                               │
│ • HOTEL_ADMIN - 酒店管理员                                 │
│ • HOTEL_STAFF - 酒店员工                                   │
│ • FINANCE_STAFF - 财务人员                                 │
│                                                            │
│ 权限定义：                                                  │
│ • package:create - 创建套餐                               │
│ • package:read - 查看套餐                                 │
│ • package:update - 更新套餐                               │
│ • package:delete - 删除套餐                               │
│ • order:create - 创建订单                                 │
│ • order:read - 查看订单                                   │
│ • payment:process - 处理支付                              │
│ • invoice:apply - 申请发票                                │
│ • invoice:review - 审核发票                               │
│                                                            │
│ 注解使用：                                                  │
│ • @PreAuthorize("hasRole('PAYMENT_ADMIN')")               │
│ • @PreAuthorize("hasAuthority('package:create')")         │
│ • @PostAuthorize("returnObject.hotelCode == principal.hotelCode") │
└─────────────────────────────────────────────────────────────┘
```

## 7. 部署运维方案

### 7.1 容器化部署架构

```
Kubernetes集群部署：
┌─────────────────────────────────────────────────────────────┐
│                    Kubernetes集群                           │
│                                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                  Ingress层                              │ │
│ │ • Nginx Ingress Controller                             │ │
│ │ • SSL证书管理                                          │ │
│ │ • 域名路由                                              │ │
│ │ • 负载均衡                                              │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                 Service层                               │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │ │
│ │ │Gateway      │ │Package      │ │Order        │         │ │
│ │ │Service      │ │Service      │ │Service      │         │ │
│ │ │             │ │             │ │             │         │ │
│ │ │Replicas: 2  │ │Replicas: 3  │ │Replicas: 3  │         │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘         │ │
│ │                                                         │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │ │
│ │ │Payment      │ │Invoice      │ │Notification │         │ │
│ │ │Service      │ │Service      │ │Service      │         │ │
│ │ │             │ │             │ │             │         │ │
│ │ │Replicas: 3  │ │Replicas: 2  │ │Replicas: 2  │         │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                │                            │
│                                ▼                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                 存储层                                   │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐         │ │
│ │ │MySQL        │ │Redis        │ │RocketMQ     │         │ │
│ │ │StatefulSet  │ │Deployment   │ │StatefulSet  │         │ │
│ │ │             │ │             │ │             │         │ │
│ │ │Master+Slave │ │Cluster      │ │Cluster      │         │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘         │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘

部署配置示例：
┌─────────────────────────────────────────────────────────────┐
│ payment-service-deployment.yaml:                          │
│                                                            │
│ apiVersion: apps/v1                                        │
│ kind: Deployment                                           │
│ metadata:                                                  │
│   name: payment-service                                    │
│ spec:                                                      │
│   replicas: 3                                              │
│   selector:                                                │
│     matchLabels:                                           │
│       app: payment-service                                 │
│   template:                                                │
│     metadata:                                              │
│       labels:                                              │
│         app: payment-service                               │
│     spec:                                                  │
│       containers:                                          │
│       - name: payment-service                              │
│         image: payment-service:latest                      │
│         ports:                                             │
│         - containerPort: 8080                              │
│         env:                                               │
│         - name: SPRING_PROFILES_ACTIVE                     │
│           value: "prod"                                    │
│         - name: NACOS_SERVER_ADDR                          │
│           value: "nacos-service:8848"                      │
│         resources:                                         │
│           requests:                                        │
│             memory: "512Mi"                                │
│             cpu: "500m"                                    │
│           limits:                                          │
│             memory: "1Gi"                                  │
│             cpu: "1000m"                                   │
│         livenessProbe:                                     │
│           httpGet:                                         │
│             path: /actuator/health                         │
│             port: 8080                                     │
│           initialDelaySeconds: 60                          │
│           periodSeconds: 30                                │
│         readinessProbe:                                    │
│           httpGet:                                         │
│             path: /actuator/health                         │
│             port: 8080                                     │
│           initialDelaySeconds: 30                          │
│           periodSeconds: 10                                │
└─────────────────────────────────────────────────────────────┘
```

这个技术方案完全采用ASCII图形风格，完美兼容飞书文档显示，包含了完整的业务流程、价格计算逻辑、系统集成和部署方案。
```
```
```
```
```