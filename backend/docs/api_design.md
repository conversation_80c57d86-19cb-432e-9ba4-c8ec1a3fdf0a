# AI运营后台付费管理系统 - API接口设计

## 概述

本文档详细描述了付费管理系统的RESTful API接口设计，包括请求参数、响应格式、错误码等详细信息。

## 通用规范

### 1. 接口规范
- **协议**: HTTPS
- **请求方式**: RESTful风格
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (yyyy-MM-dd'T'HH:mm:ss.SSS'Z')

### 2. 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2025-01-10T10:30:00.000Z",
  "traceId": "abc123def456"
}
```

### 3. 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [],
    "total": 100,
    "size": 20,
    "current": 1,
    "pages": 5
  },
  "timestamp": "2025-01-10T10:30:00.000Z",
  "traceId": "abc123def456"
}
```

### 4. 错误码规范
| 错误码 | 说明 | HTTP状态码 |
|--------|------|------------|
| 200 | 成功 | 200 |
| 400 | 参数错误 | 400 |
| 401 | 未授权 | 401 |
| 403 | 无权限 | 403 |
| 404 | 资源不存在 | 404 |
| 500 | 系统错误 | 500 |
| 1001 | 套餐不存在 | 400 |
| 1002 | 套餐已停用 | 400 |
| 2001 | 订单不存在 | 400 |
| 2002 | 订单状态错误 | 400 |
| 3001 | 支付失败 | 400 |
| 4001 | 发票申请失败 | 400 |

## 套餐管理API

### 1. 创建套餐
**接口**: `POST /api/v1/packages`

**请求参数**:
```json
{
  "packageName": "基础套餐",
  "paymentMode": 0,
  "discountMode": 2,
  "isRecommended": false,
  "products": [
    {
      "productName": "AI客服",
      "productDescription": ["智能问答", "24小时在线"],
      "sortOrder": 1,
      "pricing": [
        {
          "periodType": "MONTHLY",
          "marketPrice": 1000.00,
          "discountPrice": 800.00,
          "finalPrice": 800.00
        }
      ]
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "packageCode": "PKG_20250110_0001",
    "packageName": "基础套餐",
    "paymentMode": 0,
    "discountMode": 2,
    "status": 1,
    "isRecommended": false,
    "createdAt": "2025-01-10T10:30:00.000Z"
  }
}
```

### 2. 查询套餐列表
**接口**: `GET /api/v1/packages`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| packageName | String | 否 | 套餐名称（模糊查询） |
| paymentMode | Integer | 否 | 付费方式 |
| status | Integer | 否 | 状态 |
| current | Integer | 否 | 当前页，默认1 |
| size | Integer | 否 | 页大小，默认20 |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "records": [
      {
        "id": 1,
        "packageCode": "PKG_20250110_0001",
        "packageName": "基础套餐",
        "paymentMode": 0,
        "paymentModeText": "按房间数量收费",
        "discountMode": 2,
        "discountModeText": "一口价",
        "status": 1,
        "statusText": "启用",
        "isRecommended": true,
        "productCount": 3,
        "createdAt": "2025-01-10T10:30:00.000Z"
      }
    ],
    "total": 10,
    "size": 20,
    "current": 1,
    "pages": 1
  }
}
```

### 3. 获取套餐详情
**接口**: `GET /api/v1/packages/{packageId}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "packageCode": "PKG_20250110_0001",
    "packageName": "基础套餐",
    "paymentMode": 0,
    "discountMode": 2,
    "status": 1,
    "isRecommended": true,
    "products": [
      {
        "id": 1,
        "productName": "AI客服",
        "productDescription": ["智能问答", "24小时在线"],
        "sortOrder": 1,
        "pricing": [
          {
            "id": 1,
            "periodType": "MONTHLY",
            "marketPrice": 1000.00,
            "discountPrice": 800.00,
            "finalPrice": 800.00
          },
          {
            "id": 2,
            "periodType": "QUARTERLY",
            "marketPrice": 3000.00,
            "discountPrice": 2000.00,
            "finalPrice": 2000.00
          }
        ]
      }
    ],
    "createdAt": "2025-01-10T10:30:00.000Z"
  }
}
```

### 4. 门店配置套餐
**接口**: `POST /api/v1/hotel-packages`

**请求参数**:
```json
{
  "hotelCode": "HOTEL_001",
  "packageId": 1,
  "customPricing": [
    {
      "productId": 1,
      "periodType": "MONTHLY",
      "customMarketPrice": 900.00,
      "customDiscountPrice": 700.00,
      "isCustomPricing": true
    }
  ]
}
```

## 订单管理API

### 1. 创建订单
**接口**: `POST /api/v1/orders`

**请求参数**:
```json
{
  "hotelCode": "HOTEL_001",
  "hotelPackageId": 1,
  "purchasePeriod": "MONTHLY",
  "roomCount": 50,
  "serviceStartTime": "2025-01-15T00:00:00.000Z",
  "remark": "测试订单"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "orderNo": "ORD_20250110_000001",
    "hotelCode": "HOTEL_001",
    "hotelName": "测试酒店",
    "packageName": "基础套餐",
    "purchasePeriod": "MONTHLY",
    "originalAmount": 1000.00,
    "discountAmount": 200.00,
    "orderAmount": 800.00,
    "orderStatus": "PENDING",
    "expireAt": "2025-01-11T10:30:00.000Z",
    "createdAt": "2025-01-10T10:30:00.000Z"
  }
}
```

### 2. 查询订单列表
**接口**: `GET /api/v1/orders`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| hotelCode | String | 否 | 酒店编码 |
| orderStatus | String | 否 | 订单状态 |
| startDate | String | 否 | 开始日期 |
| endDate | String | 否 | 结束日期 |
| current | Integer | 否 | 当前页 |
| size | Integer | 否 | 页大小 |

### 3. 订单统计
**接口**: `GET /api/v1/orders/statistics`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalOrders": 1000,
    "paidOrders": 800,
    "pendingOrders": 150,
    "cancelledOrders": 50,
    "totalAmount": 500000.00,
    "paidAmount": 400000.00,
    "todayOrders": 20,
    "todayAmount": 10000.00
  }
}
```

## 支付管理API

### 1. 发起支付
**接口**: `POST /api/v1/payments/pay`

**请求参数**:
```json
{
  "orderNo": "ORD_20250110_000001",
  "paymentMethod": "WECHAT",
  "returnUrl": "https://example.com/return",
  "notifyUrl": "https://example.com/notify"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "paymentId": "PAY_20250110_000001",
    "paymentUrl": "https://pay.weixin.qq.com/...",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "expireTime": "2025-01-10T11:30:00.000Z"
  }
}
```

### 2. 查询支付状态
**接口**: `GET /api/v1/payments/status/{orderNo}`

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderNo": "ORD_20250110_000001",
    "paymentStatus": "SUCCESS",
    "transactionNo": "TXN_20250110_000001",
    "paymentAmount": 800.00,
    "paymentMethod": "WECHAT",
    "paidAt": "2025-01-10T10:35:00.000Z"
  }
}
```

### 3. 申请退款
**接口**: `POST /api/v1/payments/refund`

**请求参数**:
```json
{
  "orderNo": "ORD_20250110_000001",
  "refundAmount": 800.00,
  "refundReason": "用户申请退款"
}
```

## 发票管理API

### 1. 保存发票信息
**接口**: `POST /api/v1/invoices/info`

**请求参数**:
```json
{
  "hotelCode": "HOTEL_001",
  "invoiceTitle": "测试公司",
  "taxNumber": "91110000000000000X",
  "receiveEmail": "<EMAIL>",
  "invoiceContent": "服务费",
  "moreInfo": "备注信息"
}
```

### 2. 申请发票
**接口**: `POST /api/v1/invoices/applications`

**请求参数**:
```json
{
  "hotelCode": "HOTEL_001",
  "billIds": [1, 2, 3],
  "invoiceTitle": "测试公司",
  "taxNumber": "91110000000000000X",
  "receiveEmail": "<EMAIL>",
  "invoiceContent": "服务费",
  "moreInfo": "备注信息"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "applicationNo": "INV_20250110_000001",
    "hotelCode": "HOTEL_001",
    "invoiceAmount": 2400.00,
    "billCount": 3,
    "applicationStatus": "PENDING",
    "createdAt": "2025-01-10T10:30:00.000Z"
  }
}
```

### 3. 查询发票申请列表
**接口**: `GET /api/v1/invoices/applications`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| hotelCode | String | 否 | 酒店编码 |
| applicationStatus | String | 否 | 申请状态 |
| startDate | String | 否 | 开始日期 |
| endDate | String | 否 | 结束日期 |

### 4. 审核发票申请
**接口**: `POST /api/v1/invoices/applications/{applicationId}/review`

**请求参数**:
```json
{
  "reviewResult": "APPROVED",
  "reviewRemark": "审核通过"
}
```

## 认证授权API

### 1. 用户登录
**接口**: `POST /api/v1/auth/login`

**请求参数**:
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "refreshToken": "eyJhbGciOiJIUzUxMiJ9...",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "name": "管理员",
      "roles": ["PAYMENT_ADMIN"],
      "permissions": ["package:create", "package:read"]
    }
  }
}
```

### 2. 刷新Token
**接口**: `POST /api/v1/auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzUxMiJ9..."
}
```

### 3. 用户登出
**接口**: `POST /api/v1/auth/logout`

## WebSocket实时通知

### 1. 连接地址
`wss://api.example.com/ws/notifications`

### 2. 消息格式
```json
{
  "type": "ORDER_PAID",
  "data": {
    "orderNo": "ORD_20250110_000001",
    "hotelCode": "HOTEL_001",
    "amount": 800.00
  },
  "timestamp": "2025-01-10T10:30:00.000Z"
}
```

### 3. 消息类型
- `ORDER_CREATED`: 订单创建
- `ORDER_PAID`: 订单支付成功
- `ORDER_CANCELLED`: 订单取消
- `INVOICE_APPLIED`: 发票申请
- `INVOICE_GENERATED`: 发票生成完成

这个API设计文档提供了完整的接口规范，支持前端开发和第三方集成。
