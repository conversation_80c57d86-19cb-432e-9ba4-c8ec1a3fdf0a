# 付费套餐列表数据结构设计说明

## 设计概述

基于需求文档和界面截图，设计了完整的付费套餐列表数据结构，支持搜索筛选、分页展示、状态管理等功能。

## 核心VO设计

### 1. PackageListVO - 套餐列表主VO

```
┌─────────────────────────────────────────────────────────────┐
│                    PackageListVO 结构                       │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ 基础信息：                                                  │
│ • id - 套餐ID                                              │
│ • packageCode - 套餐编码（系统生成）                        │
│ • packageName - 套餐名称                                   │
│                                                            │
│ 业务配置：                                                  │
│ • paymentMode/paymentModeText - 付费模式及文本              │
│ • discountMode/discountModeText - 优惠方式及文本           │
│                                                            │
│ 状态管理：                                                  │
│ • status/statusText - 启用状态及文本                       │
│ • isRecommended/recommendedText - 推荐状态及文本           │
│                                                            │
│ 产品信息：                                                  │
│ • productNames - 产品名称列表（用于快速展示）               │
│ • productPricingDetails - 详细价格信息列表                 │
│                                                            │
│ 时间信息：                                                  │
│ • createdAt - 创建时间                                     │
└─────────────────────────────────────────────────────────────┘
```

### 2. ProductPricingDetailVO - 产品价格明细

```
┌─────────────────────────────────────────────────────────────┐
│                ProductPricingDetailVO 结构                  │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ 产品基础信息：                                              │
│ • productId - 产品ID                                       │
│ • productName - 产品名称                                   │
│ • sortOrder - 排序权重                                     │
│                                                            │
│ 月度价格：                                                  │
│ • monthlyMarketPrice - 月度门市价                          │
│ • monthlyDiscountPrice - 月度优惠价                        │
│ • monthlyPriceText - 月度价格显示文本                      │
│                                                            │
│ 季度价格：                                                  │
│ • quarterlyMarketPrice - 季度门市价                        │
│ • quarterlyDiscountPrice - 季度优惠价                      │
│ • quarterlyPriceText - 季度价格显示文本                    │
│ • quarterlyDiscountText - 季度折扣力度显示                 │
│                                                            │
│ 年度价格：                                                  │
│ • yearlyMarketPrice - 年度门市价                           │
│ • yearlyDiscountPrice - 年度优惠价                         │
│ • yearlyPriceText - 年度价格显示文本                       │
│ • yearlyDiscountText - 年度折扣力度显示                    │
└─────────────────────────────────────────────────────────────┘
```

## 查询和分页设计

### 1. PackageQueryRequest - 查询请求

```
┌─────────────────────────────────────────────────────────────┐
│                   查询条件设计                               │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ 搜索条件：                                                  │
│ • packageName - 套餐名称模糊搜索                           │
│ • paymentMode - 付费模式筛选                               │
│ • discountMode - 优惠方式筛选                              │
│ • status - 状态筛选                                        │
│ • isRecommended - 推荐状态筛选                             │
│                                                            │
│ 分页参数：                                                  │
│ • current - 当前页码（默认1）                              │
│ • size - 每页大小（默认20，最大100）                       │
│                                                            │
│ 排序参数：                                                  │
│ • sortField - 排序字段（默认created_at）                   │
│ • sortOrder - 排序方式（默认desc）                         │
└─────────────────────────────────────────────────────────────┘
```

### 2. PackagePageResponse - 分页响应

```
┌─────────────────────────────────────────────────────────────┐
│                   分页响应设计                               │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ 数据列表：                                                  │
│ • records - 套餐列表数据                                   │
│                                                            │
│ 分页信息：                                                  │
│ • total - 总记录数                                         │
│ • size - 每页大小                                          │
│ • current - 当前页码                                       │
│ • pages - 总页数                                           │
│ • hasPrevious - 是否有上一页                               │
│ • hasNext - 是否有下一页                                   │
└─────────────────────────────────────────────────────────────┘
```

## 操作相关VO设计

### 1. 状态操作

```
┌─────────────────────────────────────────────────────────────┐
│                   状态操作设计                               │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ StatusUpdateRequest：                                      │
│ • packageId - 套餐ID                                       │
│ • status - 目标状态（1-启用，0-停用）                      │
│ • reason - 操作原因                                        │
│                                                            │
│ RecommendUpdateRequest：                                   │
│ • packageId - 套餐ID                                       │
│ • isRecommended - 推荐状态（1-是，0-否）                   │
│ • reason - 操作原因                                        │
│                                                            │
│ OperationConfirmResponse：                                 │
│ • operationType - 操作类型                                 │
│ • confirmMessage - 确认提示文案                            │
│ • warningMessage - 警告信息                                │
│ • impactDescription - 影响范围描述                         │
└─────────────────────────────────────────────────────────────┘
```

## 数据转换逻辑

### 1. 价格文本格式化

```java
/**
 * 价格文本格式化示例
 */
public class PriceTextFormatter {
    
    /**
     * 格式化价格显示文本
     * @param marketPrice 门市价
     * @param discountPrice 优惠价
     * @return 格式化后的文本，如 "¥999.00 / ¥89.00"
     */
    public static String formatPriceText(BigDecimal marketPrice, BigDecimal discountPrice) {
        return String.format("¥%.2f / ¥%.2f", marketPrice, discountPrice);
    }
    
    /**
     * 格式化折扣力度显示
     * @param discountRate 折扣比例
     * @param periodType 周期类型
     * @return 折扣显示文本，如 "立省15%" 或 "-"
     */
    public static String formatDiscountText(BigDecimal discountRate, String periodType) {
        if ("MONTHLY".equals(periodType) || discountRate == null) {
            return "-";
        }
        return String.format("立省%.1f%%", discountRate);
    }
}
```

### 2. 状态文本转换

```java
/**
 * 状态文本转换示例
 */
public class StatusTextConverter {
    
    /**
     * 付费模式文本转换
     */
    public static String getPaymentModeText(Integer paymentMode) {
        return switch (paymentMode) {
            case 0 -> "按房间/月";
            case 1 -> "按酒店/月";
            default -> "未知";
        };
    }
    
    /**
     * 优惠方式文本转换
     */
    public static String getDiscountModeText(Integer discountMode) {
        return switch (discountMode) {
            case 1 -> "折扣";
            case 2 -> "一口价";
            default -> "未知";
        };
    }
    
    /**
     * 状态文本转换
     */
    public static String getStatusText(Integer status) {
        return switch (status) {
            case 1 -> "启用";
            case 0 -> "停用";
            default -> "未知";
        };
    }
    
    /**
     * 推荐状态文本转换
     */
    public static String getRecommendedText(Integer isRecommended) {
        return switch (isRecommended) {
            case 1 -> "是";
            case 0 -> "否";
            default -> "否";
        };
    }
}
```

## 前端展示效果

### 1. 列表展示格式

```
┌─────────────────────────────────────────────────────────────┐
│ 套餐编码: PKG_20240115_0001                                 │
│ 套餐名称: 标准版协议                                        │
│ 付费模式: 按房间/月                                         │
│ 优惠方式: 一口价                                            │
│                                                            │
│ 产品价格明细:                                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 产品      │ 月度门市价/优惠价 │ 季度门市价/优惠价 │ 年度门市价/优惠价 │ │
│ │ 微分Agent │ ¥999.00/¥89.00  │ ¥2997.00/¥267.00│ ¥11988.00/¥1068.00│ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                            │
│ 状态: 启用    推荐: 是    创建时间: 2024-01-15              │
│                                                            │
│ [编辑] [停用] [取消推荐]                                    │
└─────────────────────────────────────────────────────────────┘
```

### 2. 操作确认弹窗

```
┌─────────────────────────────────────────────────────────────┐
│                      操作确认                                │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│ 确认停用套餐？                                              │
│                                                            │
│ ⚠️  停用后该套餐将无法被门店选择购买                        │
│                                                            │
│ 影响范围：当前有15个门店正在使用此套餐                      │
│                                                            │
│                                    [取消]  [确认]          │
└─────────────────────────────────────────────────────────────┘
```

## 数据库查询优化

### 1. 查询SQL示例

```sql
-- 套餐列表查询（包含产品信息）
SELECT 
    pkg.id,
    pkg.package_code,
    pkg.package_name,
    pkg.payment_mode,
    pkg.discount_mode,
    pkg.status,
    pkg.is_recommended,
    pkg.created_at,
    GROUP_CONCAT(prd.product_name) as product_names
FROM hds_payment_package pkg
LEFT JOIN hds_payment_product prd ON pkg.id = prd.package_id
WHERE pkg.row_status = 1
  AND (pkg.package_name LIKE CONCAT('%', ?, '%') OR ? IS NULL)
  AND (pkg.payment_mode = ? OR ? IS NULL)
  AND (pkg.discount_mode = ? OR ? IS NULL)
  AND (pkg.status = ? OR ? IS NULL)
GROUP BY pkg.id
ORDER BY pkg.is_recommended DESC, pkg.created_at DESC
LIMIT ?, ?;

-- 产品价格明细查询
SELECT 
    prd.id as product_id,
    prd.product_name,
    prd.sort_order,
    pri.period_type,
    pri.market_price,
    pri.discount_price,
    pri.discount_rate,
    pri.final_price
FROM hds_payment_product prd
LEFT JOIN hds_product_pricing pri ON prd.id = pri.product_id
WHERE prd.package_id IN (?)
ORDER BY prd.sort_order, 
         FIELD(pri.period_type, 'MONTHLY', 'QUARTERLY', 'YEARLY');
```

这个数据结构设计完全基于您的需求和界面截图，支持完整的搜索、分页、状态管理功能，并且考虑了前端展示的便利性。
