​
wormhole-hotelds接口文档-二期​
​
​
认证方式: Bearer Token​
响应格式: JSON​
时间格式: ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)​
2.1、公共请求头​
​
名称​
是否必填​
描述​
Authorization​
是​
Bearer Token, 例如: Bearer eyJhbGciOiJ...​
Content-Type​
是​
固定值: application/json​
X-Request-ID​
否​
请求追踪ID，建议携带​
source​
是​
默认传 hotelds​
​
2.2、公共响应格式​
2.2.1、成功响应​
​
代码块​
{​
    "code": "0",​
    "message": "success",​
    "traceId": "550e8400-e29b-41d4-a716-446655440000",​
    "data": {​
        // 具体的业务数据​
    }​
}​
​
2.2、错误响应​
​
代码块​
{​
    "code": "ERROR_CODE",​
    "message": "错误描述信息",​
    "traceId": "550e8400-e29b-41d4-a716-446655440000",​
    "success": false,​
    "success_with_data": false​
}​
​
2.3、注意事项​
与前端所有参数均使用下划线格式​
所有时间字段均使用 UTC 时间，格式为 ISO 8601​
分页接口默认每页大小为 20，最大不超过 100​
批量操作接口单次请求最多处理 100 条数据​
接口调用频率限制为 100次/分钟​
接口详情​

上传日志
联系客服
功能更新
帮助中心
效率指南


解释


根据这份接口文档，帮我完成下面表的接口文档

1. 数据库表结构设计
1.1 付费套餐主表（hds_payment_package）
create table hds_payment_package
(
    id              int auto_increment comment '主键'      primary key,
    package_code    varchar(64)                           not null comment '套餐编码，唯一标识',
    package_name    varchar(64)                           not null comment '套餐名称，唯一',
    payment_mode    tinyint                               not null comment '付费方式：0-按房间数量收费，1-按门店收费',
    discount_mode   tinyint                               not null comment '优惠方式：1-折扣，2-一口价',
    status          tinyint     default 1                 not null comment '状态：1-启用，0-停用',
    is_recommended  tinyint     default 0                 not null comment '是否推荐：1-是，0-否',
    created_by      varchar(64) default '1'               not null comment '创建人id',
    created_by_name varchar(64) default '1'               not null comment '创建人名称',
    updated_by      varchar(64) default '1'               not null comment '修改人id',
    updated_by_name varchar(64) default '1'               not null comment '修改人名称',
    created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_package_code
        unique (package_code),
    constraint uk_package_name
        unique (package_name)
)
    comment '付费套餐主表';

create index idx_payment_mode on hds_payment_package (payment_mode);
create index idx_discount_mode on hds_payment_package (discount_mode);
create index idx_status on hds_payment_package (status);
create index idx_is_recommended on hds_payment_package (is_recommended);
1.2 付费产品表（hds_payment_product）
create table hds_payment_product
(
    id                  int auto_increment comment '主键'      primary key,
    package_id          int                                   not null comment '套餐ID，关联hds_payment_package.id',
    product_name        varchar(64)                           not null comment '产品名称',
    product_description json                                  null comment '产品描述JSON数组，最多10条，每条最大50字符',
    sort_order          int         default 0                 not null comment '产品在套餐中的排序',
    created_by          varchar(64) default '1'               not null comment '创建人id',
    created_by_name     varchar(64) default '1'               not null comment '创建人名称',
    updated_by          varchar(64) default '1'               not null comment '修改人id',
    updated_by_name     varchar(64) default '1'               not null comment '修改人名称',
    created_at          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at          datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status          int         default 1                 not null comment '记录状态(1:有效, 0:无效)'
)
    comment '付费产品表';

create index idx_package_id on hds_payment_product (package_id);
create index idx_sort_order on hds_payment_product (sort_order);
1.3 产品标准定价表
create table hds_product_pricing
(
    id              int auto_increment comment '主键'     primary key,
    product_id      int                                   not null comment '产品ID，关联hds_payment_product.id',
    period_type tinyint                               not null comment '周期类型：1-月度，2-季度，3-年度',
    market_price    decimal(10,2)                         not null comment '门市价',
    discount_price  decimal(10,2)                         null comment '优惠价（一口价模式使用）',
    discount_rate   decimal(5,2)                          null comment '折扣比例，百分比（折扣模式使用）',
    final_price     decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    created_by      varchar(64) default '1'               not null comment '创建人id',
    created_by_name varchar(64) default '1'               not null comment '创建人名称',
    updated_by      varchar(64) default '1'               not null comment '修改人id',
    updated_by_name varchar(64) default '1'               not null comment '修改人名称',
    created_at      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status      int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_product_period
        unique (product_id, period_type)
)
    comment '产品定价表';

create index idx_product_id on hds_product_pricing (product_id);
create index idx_period_type on hds_product_pricing (period_type);
create index idx_final_price on hds_product_pricing (final_price);
1.4 门店付费套餐表
-- 门店付费套餐表
create table hds_hotel_payment_package
(
    id                      int auto_increment comment '主键'     primary key,
    hotel_package_code      varchar(64)                           not null comment '门店套餐编码',
    hotel_code              varchar(64)                           not null comment '酒店编码',
    package_id              int                                   not null comment '套餐ID',
    status                  tinyint     default 0                 not null comment '状态：1-启用，0-停用',
    created_by              varchar(64) default '1'               not null comment '创建人id',
    created_by_name         varchar(64) default '1'               not null comment '创建人名称',
    updated_by              varchar(64) default '1'               not null comment '修改人id',
    updated_by_name         varchar(64) default '1'               not null comment '修改人名称',
    created_at              datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at              datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status              int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_package_code
        unique (hotel_package_code)
)
comment '门店付费套餐表';

create index idx_hotel_code on hds_hotel_payment_package (hotel_code);
create index idx_package_id on hds_hotel_payment_package (package_id);
create index idx_status on hds_hotel_payment_package (status);
1.5 门店套餐产品定价表（个性化定价：hds_hotel_product_pricing）
create table hds_hotel_product_pricing
(
    id                    int auto_increment comment '主键'      primary key,
    hotel_package_id      int                                   not null comment '门店套餐ID，关联hds_hotel_package.id',
    product_id            int                                   not null comment '产品ID，关联hds_payment_product.id',
    period_type           tinyint                               not null comment '周期类型：1-月度，2-季度，3-年度',
    custom_market_price   decimal(10,2)                         null comment '自定义门市价，为空则使用标准价格',
    custom_discount_price decimal(10,2)                         null comment '自定义优惠价，为空则使用标准价格',
    custom_discount_rate  decimal(5,2)                          null comment '自定义折扣比例，为空则使用标准比例',
    final_price           decimal(10,2)                         not null comment '最终价格（计算后的实际价格）',
    is_custom_pricing     tinyint     default 0                 not null comment '是否使用自定义定价：0-使用标准价格，1-使用自定义价格',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_hotel_product_period
        unique (hotel_package_id, product_id, period_type)
)
    comment '门店个性化定价表';

create index idx_hotel_package_id on hds_hotel_product_pricing (hotel_package_id);
create index idx_product_id_hotel on hds_hotel_product_pricing (product_id);
create index idx_period_type_hotel on hds_hotel_product_pricing (period_type);
create index idx_final_price_hotel on hds_hotel_product_pricing (final_price);
create index idx_is_custom_pricing on hds_hotel_product_pricing (is_custom_pricing);
1.6 交易订单表
create table hds_payment_order
(
    id                    int auto_increment comment '主键'      primary key,
    order_no              varchar(64)                           not null comment '订单编号，系统自动生成',
    hotel_code            varchar(64)                           not null comment '酒店编码，关联hds_hotel_info.hotel_code',
    hotel_package_id      int                                   not null comment '门店套餐ID，关联hds_hotel_payment_package.id',
    period_type       tinyint                                   not null comment '周期类型：1-月度，2-季度，3-年度',
    room_count            int                                   null comment '房间数量（按房间收费时记录）',
    original_amount       decimal(10,2)                         not null comment '原价金额',
    discount_amount       decimal(10,2)                         not null default 0.00 comment '优惠金额',
    order_amount          decimal(10,2)                         not null comment '订单金额（原价-优惠）',
    expire_time           datetime                              null comment '产品到期时间',
    order_status          tinyint     default 1                 not null comment '订单状态：0-待支付，1-已支付，2-已取消',
    payment_method        tinyint                               not null comment '支付方式：1-微信，2-支付宝',
    paid_at               datetime                              null comment '支付时间',
    expire_at             datetime                              null comment '订单过期时间',
    remark                text                                  null comment '备注信息',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_order_no
        unique (order_no)
)
    comment '交易订单表';

create index idx_hotel_code on hds_payment_order (hotel_code);
create index idx_hotel_package_id on hds_payment_order (hotel_package_id);
create index idx_order_status on hds_payment_order (order_status);
create index idx_purchase_period on hds_payment_order (purchase_period);
create index idx_created_at on hds_payment_order (created_at);
create index idx_service_time on hds_payment_order (service_start_time, service_end_time);
1.7 交易订单明细表
create table hds_order_detail
(
    id                    int auto_increment comment '主键'     primary key,
    order_id              int                                   not null comment '订单ID，关联hds_payment_order.id',
    product_id            int                                   not null comment '产品ID，关联hds_payment_product.id',
    product_name          varchar(64)                           not null comment '产品名称（冗余存储，保证历史数据准确性）',
    period_type           tinyint                               not null comment '周期类型：1-月度，2-季度，3-年度',
    unit_price            decimal(10,2)                         not null comment '单价',
    quantity              int                                   not null default 1 comment '数量',
    amount                decimal(10,2)                         not null comment '小计金额',
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
)
    comment '订单明细表';

create index idx_order_id on hds_order_detail (order_id);
create index idx_product_id on hds_order_detail (product_id);
create index idx_period_type on hds_order_detail (period_type);
1.8 账单表
create table hds_payment_bill
(
    id                    int auto_increment comment '主键'      primary key,
    bill_no               varchar(64)                           not null comment '账单编号，系统自动生成',
    transaction_no        varchar(64)                           not null comment '流水号（从支付平台获取）',
    order_id              int                                   not null comment '订单ID，关联hds_payment_order.id',
    hotel_code            varchar(64)                           not null comment '酒店编码',
    payment_amount        decimal(10,2)                         not null comment '支付金额',
    payment_method        tinyint                               not null comment '支付方式：1-微信，2-支付宝',
    transaction_status    tinyint     default 1                 not null comment '交易状态：1-交易成功，2-已退款',
    transaction_at        datetime                              not null comment '交易完成时间',
    refund_amount         decimal(10,2)                         null comment '退款金额',
    refund_at             datetime                              null comment '退款时间',
    refund_reason         varchar(255)                          null comment '退款原因',
    is_invoice_applied    tinyint default 0 not                 null comment '是否已申请发票：0-未申请，1-已申请'
    created_by            varchar(64) default '1'               not null comment '创建人id',
    created_by_name       varchar(64) default '1'               not null comment '创建人名称',
    updated_by            varchar(64) default '1'               not null comment '修改人id',
    updated_by_name       varchar(64) default '1'               not null comment '修改人名称',
    created_at            datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at            datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    row_status            int         default 1                 not null comment '记录状态(1:有效, 0:无效)',
    constraint uk_transaction_no
        unique (transaction_no),
    constraint uk_bill_no
        unique (bill_no)
)
    comment '账单表';

create index idx_order_id on hds_payment_bill (order_id);
create index idx_hotel_code on hds_payment_bill (hotel_code);
create index idx_transaction_status on hds_payment_bill (transaction_status);
create index idx_transaction_at on hds_payment_bill (transaction_at);
create index idx_invoice_status on hds_payment_bill (invoice_status);
1.9 门店开发票基础信息表
CREATE TABLE hds_invoice_info (
    id              INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    hotel_code      VARCHAR(64) NOT NULL COMMENT '门店编码',
    invoice_header   VARCHAR(128) NOT NULL COMMENT '发票抬头',
    tax_number      VARCHAR(32) NOT NULL COMMENT '纳税人识别号',
    receive_email   VARCHAR(128) NOT NULL COMMENT '接收邮箱',
    invoice_content VARCHAR(64) DEFAULT '服务费' COMMENT '发票内容',
    more_info       TEXT COMMENT '更多信息/备注',
    created_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by      VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status      INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    UNIQUE KEY uk_hotel_code (hotel_code),
    KEY idx_tax_number (tax_number)
) COMMENT='发票信息表';
1.10 门店开发票申请记录表
CREATE TABLE hds_invoice_application (
    id                    BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    hotel_code            VARCHAR(64) NOT NULL COMMENT '酒店编码',
    application_no        VARCHAR(64) NOT NULL COMMENT '申请编号',
    invoice_amount        DECIMAL(10,2) NOT NULL COMMENT '发票金额',
    bill_count            INT NOT NULL COMMENT '关联账单数量', -- 新增
    application_status    TINYINT DEFAULT 0 COMMENT '申请状态：0-待审核，1-审核中，2-已开票，3-已驳回',
    review_remark         TEXT COMMENT '审核备注',
    reviewed_by           VARCHAR(64) COMMENT '审核人',
    reviewed_by_name      VARCHAR(64) COMMENT '审核人名称',
    reviewed_at           DATETIME COMMENT '审核时间',
    invoice_no            VARCHAR(64) COMMENT '发票号码',
    invoice_code          VARCHAR(32) COMMENT '发票代码',
    invoice_url           VARCHAR(512) COMMENT '发票文件URL',
    invoiced_at           DATETIME COMMENT '开票时间',
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',

    -- 索引
    UNIQUE KEY uk_application_no (application_no),
    KEY idx_hotel_code (hotel_code),
    KEY idx_application_status (application_status),
    KEY idx_created_at (created_at),
    KEY idx_invoice_no (invoice_no)
) COMMENT='发票申请表';
1.11 门店开发票申请记录详情表
CREATE TABLE hds_invoice_detail (
    id                INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    application_id    INT NOT NULL COMMENT '发票申请ID',
    bill_id           INT NOT NULL COMMENT '账单ID',
    bill_no           VARCHAR(64) NOT NULL COMMENT '账单编号',
    order_no          VARCHAR(64) NOT NULL COMMENT '订单编号',
    package_name      VARCHAR(64) NOT NULL COMMENT '套餐名称',
    bill_amount       DECIMAL(10,2) NOT NULL COMMENT '账单金额',
    created_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人id',
    created_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '创建人名称',
    updated_by            VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人id',
    updated_by_name       VARCHAR(64) DEFAULT '1' NOT NULL COMMENT '修改人名称',
    created_at            DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
    updated_at            DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL COMMENT '更新时间',
    row_status            INT DEFAULT 1 NOT NULL COMMENT '记录状态(1:有效, 0:无效)',
    KEY idx_application_id (application_id),
    KEY idx_bill_id (bill_id),
    KEY idx_order_no (order_no),
    FOREIGN KEY fk_invoice_application (application_id) REFERENCES hds_invoice_application(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票明细表';