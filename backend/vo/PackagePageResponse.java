package com.hds.payment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 付费套餐分页响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "付费套餐分页响应")
public class PackagePageResponse {

    @Schema(description = "套餐列表")
    private List<PackageListVO> records;

    @Schema(description = "总记录数", example = "100")
    private Long total;

    @Schema(description = "每页大小", example = "20")
    private Long size;

    @Schema(description = "当前页码", example = "1")
    private Long current;

    @Schema(description = "总页数", example = "5")
    private Long pages;

    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;

    @Schema(description = "是否有下一页")
    private Boolean hasNext;
}
