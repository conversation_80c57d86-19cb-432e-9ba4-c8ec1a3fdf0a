package com.hds.payment.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 付费套餐列表响应VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "付费套餐列表响应")
public class PackageListVO {

    @Schema(description = "套餐ID")
    private Long id;

    @Schema(description = "套餐编码", example = "PKG_20240115_0001")
    private String packageCode;

    @Schema(description = "套餐名称", example = "标准版协议")
    private String packageName;

    @Schema(description = "付费模式：0-按房间数量收费，1-按门店收费")
    private Integer paymentMode;

    @Schema(description = "付费模式文本", example = "按房间/月")
    private String paymentModeText;

    @Schema(description = "优惠方式：1-折扣，2-一口价")
    private Integer discountMode;

    @Schema(description = "优惠方式文本", example = "按房间/月")
    private String discountModeText;

    @Schema(description = "套餐状态：1-启用，0-停用")
    private Integer status;

    @Schema(description = "套餐状态文本", example = "启用")
    private String statusText;

    @Schema(description = "是否推荐：1-是，0-否")
    private Integer isRecommended;

    @Schema(description = "推荐状态文本", example = "是")
    private String recommendedText;

    @Schema(description = "包含产品列表")
    private List<String> productNames;

    @Schema(description = "产品价格明细列表")
    private List<ProductPricingDetailVO> productPricingDetails;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createdAt;

    /**
     * 产品价格明细VO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "产品价格明细")
    public static class ProductPricingDetailVO {

        @Schema(description = "产品ID")
        private Long productId;

        @Schema(description = "产品名称", example = "微分Agent")
        private String productName;

        @Schema(description = "月度门市价", example = "999.00")
        private BigDecimal monthlyMarketPrice;

        @Schema(description = "月度优惠价", example = "89.00")
        private BigDecimal monthlyDiscountPrice;

        @Schema(description = "月度价格显示文本", example = "¥999.00 / ¥89.00")
        private String monthlyPriceText;

        @Schema(description = "季度门市价", example = "2997.00")
        private BigDecimal quarterlyMarketPrice;

        @Schema(description = "季度优惠价", example = "267.00")
        private BigDecimal quarterlyDiscountPrice;

        @Schema(description = "季度价格显示文本", example = "¥2997.00 / ¥267.00")
        private String quarterlyPriceText;

        @Schema(description = "季度折扣力度", example = "立省15%")
        private String quarterlyDiscountText;

        @Schema(description = "年度门市价", example = "11988.00")
        private BigDecimal yearlyMarketPrice;

        @Schema(description = "年度优惠价", example = "1068.00")
        private BigDecimal yearlyDiscountPrice;

        @Schema(description = "年度价格显示文本", example = "¥11988.00 / ¥1068.00")
        private String yearlyPriceText;

        @Schema(description = "年度折扣力度", example = "立省25%")
        private String yearlyDiscountText;

        @Schema(description = "产品排序")
        private Integer sortOrder;
    }
}
