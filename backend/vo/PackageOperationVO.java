package com.hds.payment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 套餐操作相关VO
 */
public class PackageOperationVO {

    /**
     * 套餐状态更新请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "套餐状态更新请求")
    public static class StatusUpdateRequest {

        @Schema(description = "套餐ID")
        @NotNull(message = "套餐ID不能为空")
        private Long packageId;

        @Schema(description = "目标状态：1-启用，0-停用")
        @NotNull(message = "目标状态不能为空")
        private Integer status;

        @Schema(description = "操作原因", example = "业务需要停用该套餐")
        private String reason;
    }

    /**
     * 推荐状态更新请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "推荐状态更新请求")
    public static class RecommendUpdateRequest {

        @Schema(description = "套餐ID")
        @NotNull(message = "套餐ID不能为空")
        private Long packageId;

        @Schema(description = "是否推荐：1-是，0-否")
        @NotNull(message = "推荐状态不能为空")
        private Integer isRecommended;

        @Schema(description = "操作原因", example = "设置为默认推荐套餐")
        private String reason;
    }

    /**
     * 操作确认响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "操作确认响应")
    public static class OperationConfirmResponse {

        @Schema(description = "操作类型", example = "STATUS_UPDATE")
        private String operationType;

        @Schema(description = "确认提示文案", example = "确认启用套餐？")
        private String confirmMessage;

        @Schema(description = "警告信息", example = "启用后该套餐将对所有门店可见")
        private String warningMessage;

        @Schema(description = "影响范围描述", example = "将影响100个门店的套餐选择")
        private String impactDescription;
    }

    /**
     * 批量操作请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "批量操作请求")
    public static class BatchOperationRequest {

        @Schema(description = "套餐ID列表")
        @NotNull(message = "套餐ID列表不能为空")
        private java.util.List<Long> packageIds;

        @Schema(description = "操作类型：ENABLE-启用，DISABLE-停用，DELETE-删除")
        @NotNull(message = "操作类型不能为空")
        private String operationType;

        @Schema(description = "操作原因")
        private String reason;
    }
}
