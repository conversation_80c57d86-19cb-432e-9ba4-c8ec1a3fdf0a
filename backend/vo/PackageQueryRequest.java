package com.hds.payment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 付费套餐查询请求VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "付费套餐查询请求")
public class PackageQueryRequest {

    @Schema(description = "套餐名称（支持模糊搜索）", example = "标准版")
    private String packageName;

    @Schema(description = "付费模式筛选：0-按房间数量收费，1-按门店收费")
    private Integer paymentMode;

    @Schema(description = "优惠方式筛选：1-折扣，2-一口价")
    private Integer discountMode;

    @Schema(description = "套餐状态筛选：1-启用，0-停用")
    private Integer status;

    @Schema(description = "是否推荐筛选：1-是，0-否")
    private Integer isRecommended;

    @Schema(description = "当前页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer current = 1;

    @Schema(description = "每页大小", example = "20")
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 20;

    @Schema(description = "排序字段", example = "created_at")
    private String sortField = "created_at";

    @Schema(description = "排序方式：asc-升序，desc-降序", example = "desc")
    private String sortOrder = "desc";
}
