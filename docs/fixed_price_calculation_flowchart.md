# 一口价计算规则流程图

## 一口价计算核心流程

```
┌─────────────────┐
    │ 开始一口价计算   │
│ 输入：产品定价   │
│ 模式：一口价     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 获取月度优惠价   │
│ • 必填项        │
│ • 基础价格      │
│ • 示例：20元    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 计算季度优惠价   │
│ 检查是否设置     │
│ 季度一口价       │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │是否设置了 │
      │季度一口价？│
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│   是     │    │   否     │
│         │    │         │
│直接使用 │    │使用默认 │
│设置的   │    │计算公式 │
│一口价   │    │         │
└────┬────┘    └────┬────┘
     │              │
     │              ▼
     │    ┌─────────────────┐
     │    │ 季度优惠价 =     │
     │    │ 月度优惠价 × 3   │
     │    │ 示例：20 × 3 = 60│
     │    └─────────┬───────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│ 计算年度优惠价   │
│ 检查是否设置     │
│ 年度一口价       │
└─────────┬───────┘
          │
          ▼
      ┌───────────┐
      │是否设置了 │
      │年度一口价？│
      └─────┬─────┘
            │
    ┌───────┴───────┐
    │               │
    ▼               ▼
┌─────────┐    ┌─────────┐
│   是     │    │   否     │
│         │    │         │
│直接使用 │    │使用默认 │
│设置的   │    │计算公式 │
│一口价   │    │         │
└────┬────┘    └────┬────┘
     │              │
     │              ▼
     │    ┌─────────────────┐
     │    │ 年度优惠价 =     │
     │    │ 月度优惠价 × 12  │
     │    │ 示例：20×12=240  │
     │    └─────────┬───────┘
     │              │
     └──────┬───────┘
            │
            ▼
┌─────────────────┐
│ 返回计算结果     │
│ • 月度优惠价    │
│ • 季度优惠价    │
│ • 年度优惠价    │
└─────────────────┘
```

## 详细计算规则说明

### 1. 月度优惠价计算

```
┌─────────────────────────────────────────────────────────────┐
│                    月度优惠价                                │
│                                                            │
│ 规则：直接使用设置的月度优惠价                              │
│ 特点：必填项，作为其他周期计算的基础                        │
│                                                            │
│ 示例：                                                      │
│ • 设置月度优惠价：20元                                      │
│ • 最终月度价格：20元                                        │
└─────────────────────────────────────────────────────────────┘
```

### 2. 季度优惠价计算

```
┌─────────────────────────────────────────────────────────────┐
│                    季度优惠价                                │
│                                                            │
│ 判断逻辑：                                                  │
│ IF 设置了季度一口价:                                        │
│     季度优惠价 = 设置的季度一口价                           │
│ ELSE:                                                      │
│     季度优惠价 = 月度优惠价 × 3                             │
│ END IF                                                     │
│                                                            │
│ 示例一（未设置季度一口价）：                                │
│ • 月度优惠价：20元                                          │
│ • 季度优惠价：20 × 3 = 60元                                 │
│                                                            │
│ 示例二（设置了季度一口价）：                                │
│ • 月度优惠价：20元                                          │
│ • 设置季度一口价：50元                                      │
│ • 季度优惠价：50元（直接使用设置值）                        │
└─────────────────────────────────────────────────────────────┘
```

### 3. 年度优惠价计算

```
┌─────────────────────────────────────────────────────────────┐
│                    年度优惠价                                │
│                                                            │
│ 判断逻辑：                                                  │
│ IF 设置了年度一口价:                                        │
│     年度优惠价 = 设置的年度一口价                           │
│ ELSE:                                                      │
│     年度优惠价 = 月度优惠价 × 12                            │
│ END IF                                                     │
│                                                            │
│ 示例一（未设置年度一口价）：                                │
│ • 月度优惠价：20元                                          │
│ • 年度优惠价：20 × 12 = 240元                               │
│                                                            │
│ 示例二（设置了年度一口价）：                                │
│ • 月度优惠价：20元                                          │
│ • 设置年度一口价：200元                                     │
│ • 年度优惠价：200元（直接使用设置值）                       │
└─────────────────────────────────────────────────────────────┘
```

## 计算示例对比

### 情况一：不设置季度/年度一口价

```
输入数据：
┌─────────────────────────────────────────────────────────────┐
│ • 月度优惠价：20元                                          │
│ • 季度一口价：未设置                                        │
│ • 年度一口价：未设置                                        │
└─────────────────────────────────────────────────────────────┘

计算过程：
┌─────────────────────────────────────────────────────────────┐
│ 1. 月度优惠价 = 20元（直接使用）                            │
│                                                            │
│ 2. 季度优惠价计算：                                         │
│    • 检查是否设置季度一口价：否                             │
│    • 使用默认公式：20 × 3 = 60元                            │
│                                                            │
│ 3. 年度优惠价计算：                                         │
│    • 检查是否设置年度一口价：否                             │
│    • 使用默认公式：20 × 12 = 240元                          │
└─────────────────────────────────────────────────────────────┘

最终结果：
┌─────────────────────────────────────────────────────────────┐
│ • 月度优惠价：20元                                          │
│ • 季度优惠价：60元                                          │
│ • 年度优惠价：240元                                         │
└─────────────────────────────────────────────────────────────┘
```

### 情况二：设置了季度/年度一口价

```
输入数据：
┌─────────────────────────────────────────────────────────────┐
│ • 月度优惠价：20元                                          │
│ • 季度一口价：50元                                          │
│ • 年度一口价：200元                                         │
└─────────────────────────────────────────────────────────────┘

计算过程：
┌─────────────────────────────────────────────────────────────┐
│ 1. 月度优惠价 = 20元（直接使用）                            │
│                                                            │
│ 2. 季度优惠价计算：                                         │
│    • 检查是否设置季度一口价：是                             │
│    • 直接使用设置值：50元                                   │
│                                                            │
│ 3. 年度优惠价计算：                                         │
│    • 检查是否设置年度一口价：是                             │
│    • 直接使用设置值：200元                                  │
└─────────────────────────────────────────────────────────────┘

最终结果：
┌─────────────────────────────────────────────────────────────┐
│ • 月度优惠价：20元                                          │
│ • 季度优惠价：50元                                          │
│ • 年度优惠价：200元                                         │
└─────────────────────────────────────────────────────────────┘
```

## 算法实现伪代码

```
FUNCTION calculateFixedPrice(monthlyPrice, quarterlyPrice, yearlyPrice):
    
    // 1. 月度优惠价（必填，直接使用）
    result.monthlyPrice = monthlyPrice
    
    // 2. 季度优惠价计算
    IF quarterlyPrice IS NOT NULL:
        result.quarterlyPrice = quarterlyPrice  // 使用设置的一口价
    ELSE:
        result.quarterlyPrice = monthlyPrice * 3  // 默认计算公式
    END IF
    
    // 3. 年度优惠价计算
    IF yearlyPrice IS NOT NULL:
        result.yearlyPrice = yearlyPrice  // 使用设置的一口价
    ELSE:
        result.yearlyPrice = monthlyPrice * 12  // 默认计算公式
    END IF
    
    RETURN result

END FUNCTION
```

## 数据库表结构建议

```
产品定价表 (hds_product_pricing)：
┌─────────────────────────────────────────────────────────────┐
│ 字段名                │ 类型          │ 说明                │
│ product_id           │ INT          │ 产品ID              │
│ period_type          │ ENUM         │ 周期类型            │
│ market_price         │ DECIMAL(10,2)│ 门市价              │
│ discount_price       │ DECIMAL(10,2)│ 优惠价（一口价）    │
│ final_price          │ DECIMAL(10,2)│ 最终价格            │
└─────────────────────────────────────────────────────────────┘

存储示例：
┌─────────────────────────────────────────────────────────────┐
│ product_id │ period_type │ market_price │ discount_price │ final_price │
│ 1          │ MONTHLY     │ 100.00       │ 20.00          │ 20.00       │
│ 1          │ QUARTERLY   │ 300.00       │ NULL           │ 60.00       │
│ 1          │ YEARLY      │ 1200.00      │ NULL           │ 240.00      │
└─────────────────────────────────────────────────────────────┘

说明：
• discount_price为NULL时，使用默认计算公式
• discount_price有值时，直接使用该值作为一口价
• final_price存储最终计算结果
```

这个流程图清晰地展示了一口价计算的完整逻辑，包括判断条件、计算公式和具体示例，完全兼容飞书文档显示。
