# 付费管理系统表关系图

## 表关系总览图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           付费管理系统表关系图                               │
└─────────────────────────────────────────────────────────────────────────────┘

                    ┌─────────────────────────────────────┐
                    │        hds_payment_package          │
                    │         (付费套餐主表)               │
                    │                                    │
                    │ • id (PK)                          │
                    │ • package_code (UK)                │
                    │ • package_name (UK)                │
                    │ • payment_mode                     │
                    │ • discount_mode                    │
                    │ • status                           │
                    │ • is_recommended                   │
                    │ • created_by                       │
                    │ • created_by_name                  │
                    │ • updated_by                       │
                    │ • updated_by_name                  │
                    │ • created_at                       │
                    │ • updated_at                       │
                    │ • row_status                       │
                    └─────────────┬───────────────────────┘
                                  │
                                  │ 1:N (一对多)
                                  │ 一个套餐包含多个产品
                                  │
                                  ▼
                    ┌─────────────────────────────────────┐
                    │        hds_payment_product          │
                    │         (付费产品表)                 │
                    │                                    │
                    │ • id (PK)                          │
                    │ • package_id (FK) ──────────────┐  │
                    │ • product_name                   │  │
                    │ • product_description (JSON)    │  │
                    │ • sort_order                     │  │
                    │ • created_by                     │  │
                    │ • created_by_name                │  │
                    │ • updated_by                     │  │
                    │ • updated_by_name                │  │
                    │ • created_at                     │  │
                    │ • updated_at                     │  │
                    │ • row_status                     │  │
                    └─────────────┬───────────────────────┘
                                  │                       │
                                  │ 1:N (一对多)          │
                                  │ 一个产品有多个        │
                                  │ 周期定价              │
                                  │                       │
                                  ▼                       │
                    ┌─────────────────────────────────────┐
                    │       hds_product_pricing           │
                    │        (产品定价表)                  │
                    │                                    │
                    │ • id (PK)                          │
                    │ • product_id (FK) ──────────────────┘
                    │ • period_type (ENUM)               │
                    │   - MONTHLY                        │
                    │   - QUARTERLY                      │
                    │   - YEARLY                         │
                    │ • market_price                     │
                    │ • discount_price                   │
                    │ • discount_rate                    │
                    │ • final_price                      │
                    │ • created_by                       │
                    │ • created_by_name                  │
                    │ • updated_by                       │
                    │ • updated_by_name                  │
                    │ • created_at                       │
                    │ • updated_at                       │
                    │ • row_status                       │
                    └─────────────────────────────────────┘
```

## 详细关系说明

### 1. 表关系类型

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              关系类型说明                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_payment_package (1) ──────── (N) hds_payment_product                  │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个套餐可以包含多个产品                                                   │
│ • 每个产品只能属于一个套餐                                                   │
│ • 外键：hds_payment_product.package_id → hds_payment_package.id           │
│                                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_payment_product (1) ──────── (N) hds_product_pricing                  │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个产品可以有多个周期的定价（月度、季度、年度）                           │
│ • 每个定价记录只能属于一个产品                                               │
│ • 外键：hds_product_pricing.product_id → hds_payment_product.id           │
│ • 唯一约束：(product_id, period_type) 确保每个产品每个周期只有一条定价      │
│                                                                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2. 数据流向图

```
数据创建流程：

┌─────────────────┐
│ 1. 创建套餐      │
│ hds_payment_    │
│ package         │
│                │
│ • 套餐基本信息  │
│ • 付费方式      │
│ • 优惠方式      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. 添加产品      │
│ hds_payment_    │
│ product         │
│                │
│ • 产品名称      │
│ • 产品描述      │
│ • 排序权重      │
│ • 关联套餐ID    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. 设置定价      │
│ hds_product_    │
│ pricing         │
│                │
│ • 月度定价      │
│ • 季度定价      │
│ • 年度定价      │
│ • 关联产品ID    │
└─────────────────┘

查询流程：

┌─────────────────┐
│ 查询套餐详情     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ JOIN 产品表      │
│ 获取产品列表     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ JOIN 定价表      │
│ 获取价格信息     │
└─────────────────┘
```

### 3. 约束关系详解

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              约束关系详解                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ 主键约束 (Primary Key)：                                                    │
│ • hds_payment_package.id                                                   │
│ • hds_payment_product.id                                                   │
│ • hds_product_pricing.id                                                   │
│                                                                            │
│ 唯一约束 (Unique Key)：                                                     │
│ • hds_payment_package.package_code (套餐编码唯一)                          │
│ • hds_payment_package.package_name (套餐名称唯一)                          │
│ • hds_product_pricing.(product_id, period_type) (产品周期定价唯一)         │
│                                                                            │
│ 外键约束 (Foreign Key)：                                                    │
│ • hds_payment_product.package_id → hds_payment_package.id                 │
│ • hds_product_pricing.product_id → hds_payment_product.id                 │
│                                                                            │
│ 枚举约束 (Enum)：                                                          │
│ • hds_product_pricing.period_type ∈ {MONTHLY, QUARTERLY, YEARLY}          │
│                                                                            │
│ 业务约束：                                                                  │
│ • payment_mode ∈ {0, 1} (0-按房间数量，1-按门店)                           │
│ • discount_mode ∈ {1, 2} (1-折扣，2-一口价)                               │
│ • status ∈ {0, 1} (0-停用，1-启用)                                         │
│ • is_recommended ∈ {0, 1} (0-否，1-是)                                    │
│                                                                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 数据示例

### 示例数据结构

```
套餐示例：
┌─────────────────────────────────────────────────────────────────────────────┐
    │ hds_payment_package                                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 1                                                                      │
│ package_code: "PKG_20250110_0001"                                         │
│ package_name: "AI客服基础套餐"                                              │
│ payment_mode: 1 (按门店收费)                                               │
│ discount_mode: 2 (一口价)                                                  │
│ status: 1 (启用)                                                           │
│ i                                            │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
产品示例：
┌─────────────────────────────────────────────────────────────────────────────┐
│ hds_payment_product                                                        │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 1                                                                      │
│ package_id: 1 (关联上面的套餐)                                             │
│ product_name: "智能客服机器人"                                              │
│ product_description: ["24小时在线", "智能问答", "多语言支持"]               │
│ sort_order: 1                                                              │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 2                                                                      │
│ package_id: 1 (关联上面的套餐)                                             │
│ product_name: "客服数据分析"                                                │
│ product_description: ["服务质量分析", "客户满意度统计"]                     │
│ sort_order: 2                                                              │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
定价示例：
┌─────────────────────────────────────────────────────────────────────────────┐
│ hds_product_pricing (产品1的定价)                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 1, product_id: 1, period_type: MONTHLY                                │
│ market_price: 1000.00, discount_price: 800.00, final_price: 800.00        │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 2, product_id: 1, period_type: QUARTERLY                              │
│ market_price: 3000.00, discount_price: NULL, final_price: 2400.00         │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 3, product_id: 1, period_type: YEARLY                                 │
│ market_price: 12000.00, discount_price: NULL, final_price: 9600.00        │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│ hds_product_pricing (产品2的定价)                                          │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 4, product_id: 2, period_type: MONTHLY                                │
│ market_price: 500.00, discount_price: 300.00, final_price: 300.00         │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 5, product_id: 2, period_type: QUARTERLY                              │
│ market_price: 1500.00, discount_price: NULL, final_price: 900.00          │
├─────────────────────────────────────────────────────────────────────────────┤
│ id: 6, product_id: 2, period_type: YEARLY                                 │
│ market_price: 6000.00, discount_price: NULL, final_price: 3600.00         │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 查询示例

### 常用查询SQL

```sql
-- 1. 查询套餐及其包含的所有产品
SELECT 
    pkg.package_name,
    pkg.payment_mode,
    pkg.discount_mode,
    prd.product_name,
    prd.sort_order
FROM hds_payment_package pkg
LEFT JOIN hds_payment_product prd ON pkg.id = prd.package_id
WHERE pkg.status = 1 AND pkg.row_status = 1
ORDER BY pkg.id, prd.sort_order;

-- 2. 查询产品的所有周期定价
SELECT 
    prd.product_name,
    pri.period_type,
    pri.market_price,
    pri.discount_price,
    pri.final_price
FROM hds_payment_product prd
LEFT JOIN hds_product_pricing pri ON prd.id = pri.product_id
WHERE prd.package_id = 1
ORDER BY prd.sort_order, 
         FIELD(pri.period_type, 'MONTHLY', 'QUARTERLY', 'YEARLY');

-- 3. 查询套餐完整信息（包含产品和定价）
SELECT 
    pkg.package_name,
    prd.product_name,
    pri.period_type,
    pri.final_price
FROM hds_payment_package pkg
LEFT JOIN hds_payment_product prd ON pkg.id = prd.package_id
LEFT JOIN hds_product_pricing pri ON prd.id = pri.product_id
WHERE pkg.id = 1
ORDER BY prd.sort_order, 
         FIELD(pri.period_type, 'MONTHLY', 'QUARTERLY', 'YEARLY');
```

这个表关系图清晰地展示了三张表之间的层次关系和数据流向，完全兼容飞书文档显示。
