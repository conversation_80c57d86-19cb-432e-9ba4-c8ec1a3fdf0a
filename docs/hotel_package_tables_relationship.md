# 门店付费套餐表关系图

## 表关系总览图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        门店付费套餐表关系图                                  │
└─────────────────────────────────────────────────────────────────────────────┘

                    ┌─────────────────────────────────────┐
                    │       hds_payment_package           │
                    │        (付费套餐主表)                │
                    │                                    │
                    │ • id (PK)                          │
                    │ • package_code (UK)                │
                    │ • package_name (UK)                │
                    │ • payment_mode                     │
                    │ • discount_mode                    │
                    │ • status                           │
                    │ • is_recommend                     │
                    │ • created_at                       │
                    │ • updated_at                       │
                    └─────────────┬───────────────────────┘
                                  │
                                  │ 1:N (一对多)
                                  │ 一个套餐可以被多个门店选择
                                  │
                                  ▼
                    ┌─────────────────────────────────────┐
                    │    hds_hotel_payment_package        │
                    │      (门店付费套餐表)                │
                    │                                    │
                    │ • id (PK)                          │
                    │ • hotel_package_code (UK)          │
                    │ • hotel_code                       │
                    │ • package_id (FK) ──────────────┐  │
                    │ • status                         │  │
                    │ • created_at                     │  │
                    │ • updated_at                     │  │
                    └─────────────┬───────────────────────┘
                                  │                       │
                                  │ 1:N (一对多)          │
                                  │ 一个门店套餐有多个    │
                                  │ 产品个性化定价        │
                                  │                       │
                                  ▼                       │
                    ┌─────────────────────────────────────┐
                    │    hds_hotel_product_pricing        │
                    │     (门店个性化定价表)               │
                    │                                    │
                    │ • id (PK)                          │
                    │ • hotel_package_id (FK) ────────────┘
                    │ • product_id                       │
                    │ • period_type                      │
                    │ • custom_market_price              │
                    │ • custom_discount_price            │
                    │ • custom_discount_rate             │
                    │ • final_price                      │
                    │ • is_custom_pricing                │
                    │ • created_at                       │
                    │ • updated_at                       │
                    └─────────────────────────────────────┘
```

## 详细关系说明

### 1. 表关系类型

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              关系类型说明                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_payment_package (1) ──────── (N) hds_hotel_payment_package            │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个套餐可以被多个门店选择                                                 │
│ • 每个门店套餐记录属于一个标准套餐                                           │
│ • 外键：hds_hotel_payment_package.package_id → hds_payment_package.id     │
│                                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_hotel_payment_package (1) ──────── (N) hds_hotel_product_pricing      │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个门店套餐可以有多个产品的个性化定价                                     │
│ • 每个个性化定价记录属于一个门店套餐                                         │
│ • 外键：hds_hotel_product_pricing.hotel_package_id → hds_hotel_payment_package.id │
│ • 唯一约束：(hotel_package_id, product_id, period_type) 确保每个产品每个周期只有一条定价 │
│                                                                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2. 业务流程关系

```
业务创建流程：

┌─────────────────┐
│ 1. 创建标准套餐  │
│ hds_payment_    │
│ package         │
│                │
│ • 套餐基本信息  │
│ • 付费方式      │
│ • 优惠方式      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. 门店选择套餐  │
│ hds_hotel_      │
│ payment_package │
│                │
│ • 选择门店      │
│ • 选择套餐      │
│ • 生成门店套餐  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. 个性化定价    │
│ hds_hotel_      │
│ product_pricing │
│                │
│ • 产品定价设置  │
│ • 周期价格配置  │
│ • 自定义价格    │
└─────────────────┘

查询流程：

┌─────────────────┐
│ 查询门店套餐     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ JOIN 标准套餐表  │
│ 获取套餐基本信息 │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ JOIN 个性化定价表│
│ 获取价格明细     │
└─────────────────┘
```

### 3. 数据流向图

```
数据流向关系：

标准套餐定义 ──→ 门店套餐选择 ──→ 个性化定价配置
     │                │                │
     ▼                ▼                ▼
套餐基本属性 ──→ 门店关联关系 ──→ 具体价格设置
     │                │                │
     ▼                ▼                ▼
付费方式        门店套餐状态      最终计算价格
优惠方式        启用/停用        自定义/标准
推荐状态        门店编码          周期定价
```

## 约束关系详解

### 1. 主键和外键约束

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              约束关系详解                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ 主键约束 (Primary Key)：                                                    │
│ • hds_payment_package.id                                                   │
│ • hds_hotel_payment_package.id                                             │
│ • hds_hotel_product_pricing.id                                             │
│                                                                            │
│ 唯一约束 (Unique Key)：                                                     │
│ • hds_payment_package.package_code (套餐编码唯一)                          │
│ • hds_payment_package.package_name (套餐名称唯一)                          │
│ • hds_hotel_payment_package.hotel_package_code (门店套餐编码唯一)          │
│ • hds_hotel_product_pricing.(hotel_package_id, product_id, period_type)   │
│   (门店套餐的每个产品每个周期只能有一条定价记录)                             │
│                                                                            │
│ 外键约束 (Foreign Key)：                                                    │
│ • hds_hotel_payment_package.package_id → hds_payment_package.id           │
│ • hds_hotel_product_pricing.hotel_package_id → hds_hotel_payment_package.id │
│                                                                            │
│ 业务约束：                                                                  │
│ • payment_mode ∈ {0, 1} (0-按房间数量，1-按门店)                           │
│ • discount_mode ∈ {0, 1} (0-折扣，1-一口价)                               │
│ • period_type ∈ {1, 2, 3} (1-月度，2-季度，3-年度)                        │
│ • status ∈ {0, 1} (0-停用，1-启用)                                         │
│ • is_recommend ∈ {0, 1} (0-否，1-是)                                      │
│ • is_custom_pricing ∈ {0, 1} (0-标准价格，1-自定义价格)                   │
│                                                                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2. 索引优化策略

```
索引设计说明：

hds_payment_package 表索引：
┌─────────────────────────────────────────────────────────────┐
│ • uk_package_code - 唯一索引，支持按编码查询                │
│ • uk_package_name - 唯一索引，支持按名称查询                │
│ • idx_payment_mode - 普通索引，支持按付费方式筛选          │
│ • idx_discount_mode - 普通索引，支持按优惠方式筛选         │
│ • idx_status - 普通索引，支持按状态筛选                    │
│ • idx_is_recommended - 普通索引，支持推荐套餐查询          │
└─────────────────────────────────────────────────────────────┘

hds_hotel_payment_package 表索引：
┌─────────────────────────────────────────────────────────────┐
│ • uk_hotel_package_code - 唯一索引，门店套餐编码唯一        │
│ • idx_hotel_code - 普通索引，支持按门店查询                │
│ • idx_package_id - 普通索引，支持按套餐查询                │
│ • idx_status - 普通索引，支持按状态筛选                    │
└─────────────────────────────────────────────────────────────┘

hds_hotel_product_pricing 表索引：
┌─────────────────────────────────────────────────────────────┐
│ • uk_hotel_product_period - 唯一索引，防止重复定价          │
│ • idx_hotel_package_id - 普通索引，支持按门店套餐查询      │
│ • idx_product_id_hotel - 普通索引，支持按产品查询          │
│ • idx_period_type_hotel - 普通索引，支持按周期查询         │
│ • idx_final_price_hotel - 普通索引，支持按价格排序         │
│ • idx_is_custom_pricing - 普通索引，筛选自定义定价         │
└─────────────────────────────────────────────────────────────┘
```

## 数据示例

### 完整业务数据示例

```
1. 标准套餐 (hds_payment_package)
┌─────────────────────────────────────────────────────────────────────────────┐
│ id: 1                                                                      │
│ package_code: "PKG_20240115_0001"                                         │
│ package_name: "AI客服基础套餐"                                              │
│ payment_mode: 1 (按门店收费)                                               │
│ discount_mode: 0 (折扣模式)                                                │
│ status: 1 (启用)                                                           │
│ is_recommend: 1 (推荐)                                                     │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
2. 门店套餐 (hds_hotel_payment_package)
┌─────────────────────────────────────────────────────────────────────────────┐
│ id: 1                                                                      │
│ hotel_package_code: "HPK_HOTEL001_PKG001_20240115"                        │
│ hotel_code: "HOTEL_001"                                                    │
│ package_id: 1 (关联上面的标准套餐)                                          │
│ status: 1 (启用)                                                           │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
3. 个性化定价 (hds_hotel_product_pricing)
┌─────────────────────────────────────────────────────────────────────────────┐
│ 月度定价：                                                                  │
│ id: 1, hotel_package_id: 1, product_id: 1, period_type: 1                 │
│ custom_market_price: NULL, custom_discount_price: 750.00                  │
│ final_price: 750.00, is_custom_pricing: 1                                 │
├─────────────────────────────────────────────────────────────────────────────┤
│ 季度定价：                                                                  │
│ id: 2, hotel_package_id: 1, product_id: 1, period_type: 2                 │
│ custom_discount_rate: 20.00, final_price: 1800.00                         │
│ is_custom_pricing: 1                                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│ 年度定价：                                                                  │
│ id: 3, hotel_package_id: 1, product_id: 1, period_type: 3                 │
│ custom_market_price: NULL, custom_discount_price: NULL                    │
│ final_price: 8000.00, is_custom_pricing: 0 (使用标准价格)                  │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 查询示例

### 常用查询SQL

```sql
-- 1. 查询门店的所有套餐配置
SELECT 
    hpp.hotel_package_code,
    hpp.hotel_code,
    pp.package_name,
    pp.payment_mode,
    pp.discount_mode,
    hpp.status
FROM hds_hotel_payment_package hpp
LEFT JOIN hds_payment_package pp ON hpp.package_id = pp.id
WHERE hpp.hotel_code = 'HOTEL_001'
  AND hpp.status = 1;

-- 2. 查询门店套餐的个性化定价
SELECT 
    hpp.hotel_package_code,
    pp.package_name,
    hpr.product_id,
    hpr.period_type,
    hpr.final_price,
    hpr.is_custom_pricing
FROM hds_hotel_payment_package hpp
LEFT JOIN hds_payment_package pp ON hpp.package_id = pp.id
LEFT JOIN hds_hotel_product_pricing hpr ON hpp.id = hpr.hotel_package_id
WHERE hpp.hotel_code = 'HOTEL_001'
ORDER BY hpr.product_id, hpr.period_type;

-- 3. 查询使用某个标准套餐的所有门店
SELECT 
    hpp.hotel_code,
    hpp.hotel_package_code,
    hpp.status,
    hpp.created_at
FROM hds_hotel_payment_package hpp
WHERE hpp.package_id = 1
  AND hpp.status = 1
ORDER BY hpp.created_at DESC;

-- 4. 查询有自定义定价的门店套餐
SELECT 
    hpp.hotel_code,
    pp.package_name,
    COUNT(hpr.id) as custom_pricing_count
FROM hds_hotel_payment_package hpp
LEFT JOIN hds_payment_package pp ON hpp.package_id = pp.id
LEFT JOIN hds_hotel_product_pricing hpr ON hpp.id = hpr.hotel_package_id 
    AND hpr.is_custom_pricing = 1
GROUP BY hpp.id, hpp.hotel_code, pp.package_name
HAVING custom_pricing_count > 0;
```

这个表关系图清晰地展示了标准套餐、门店套餐配置、个性化定价之间的层次关系和数据流向，完全兼容飞书文档显示。
