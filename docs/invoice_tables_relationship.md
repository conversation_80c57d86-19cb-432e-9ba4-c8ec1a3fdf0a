# 发票管理表关系设计

## 发票表关系总览图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           发票管理表关系图                                   │
└─────────────────────────────────────────────────────────────────────────────┘

                    ┌─────────────────────────────────────┐
                    │        hds_invoice_info             │
                    │         (发票信息表)                 │
                    │                                    │
                    │ • id (PK)                          │
                    │ • hotel_code (UK)                  │
                    │ • invoice_title                    │
                    │ • tax_number                       │
                    │ • receive_email                    │
                    │ • invoice_content                  │
                    │ • more_info                        │
                    │ • created_at                       │
                    │ • updated_at                       │
                    └─────────────┬───────────────────────┘
                                  │
                                  │ 1:N (一对多)
                                  │ 一个门店可以有多个发票申请
                                  │
                                  ▼
                    ┌─────────────────────────────────────┐
                    │      hds_invoice_application        │
                    │        (发票申请表)                  │
                    │                                    │
                    │ • id (PK)                          │
                    │ • application_no (UK)              │
                    │ • hotel_code (FK) ──────────────┐  │
                    │ • hotel_name                     │  │
                    │ • invoice_title                  │  │
                    │ • tax_number                     │  │
                    │ • receive_email                  │  │
                    │ • invoice_content                │  │
                    │ • invoice_amount                 │  │
                    │ • bill_count                     │  │
                    │ • application_status             │  │
                    │ • review_result                  │  │
                    │ • invoice_no                     │  │
                    │ • invoice_code                   │  │
                    │ • invoice_url                    │  │
                    │ • created_at                     │  │
                    │ • invoiced_at                    │  │
                    └─────────────┬───────────────────────┘
                                  │                       │
                                  │ 1:N (一对多)          │
                                  │ 一个申请包含多个      │
                                  │ 账单明细              │
                                  │                       │
                                  ▼                       │
                    ┌─────────────────────────────────────┐
                    │       hds_invoice_detail            │
                    │        (发票明细表)                  │
                    │                                    │
                    │ • id (PK)                          │
                    │ • application_id (FK) ──────────────┘
                    │ • bill_id                          │
                    │ • bill_no                          │
                    │ • order_no                         │
                    │ • package_name                     │
                    │ • service_period                   │
                    │ • service_start_time               │
                    │ • service_end_time                 │
                    │ • bill_amount                      │
                    │ • created_at                       │
                    └─────────────────────────────────────┘
                                  │
                                  │ 关联关系
                                  │ 通过bill_id关联账单表
                                  │
                                  ▼
                    ┌─────────────────────────────────────┐
                    │       hds_payment_bill              │
                    │         (账单表)                     │
                    │                                    │
                    │ • id (PK)                          │
                    │ • bill_no                          │
                    │ • order_id                         │
                    │ • hotel_code                       │
                    │ • payment_amount                   │
                    │ • invoice_status                   │
                    │ • transaction_at                   │
                    └─────────────────────────────────────┘
```

## 表关系详细说明

### 1. 关系类型

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              关系类型说明                                    │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_invoice_info (1) ──────── (N) hds_invoice_application                 │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个门店可以有多个发票申请                                                 │
│ • 每个发票申请属于一个门店                                                   │
│ • 通过hotel_code关联                                                       │
│                                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_invoice_application (1) ──────── (N) hds_invoice_detail               │
│                                                                            │
│ 关系说明：                                                                  │
│ • 一个发票申请可以包含多个账单明细                                           │
│ • 每个账单明细属于一个发票申请                                               │
│ • 外键：hds_invoice_detail.application_id → hds_invoice_application.id    │
│                                                                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                            │
│ hds_invoice_detail (N) ──────── (1) hds_payment_bill                      │
│                                                                            │
│ 关系说明：                                                                  │
│ • 多个发票明细可以关联同一个账单                                             │
│ • 每个发票明细对应一个具体账单                                               │
│ • 通过bill_id关联                                                          │
│                                                                            │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 2. 业务流程关系

```
业务流程中的表关系：

┌─────────────────┐
│ 1. 门店预设     │
│ 发票信息        │
│ (hds_invoice_   │
│  info)          │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 2. 门店申请     │
│ 发票            │
│ (hds_invoice_   │
│  application)   │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 3. 选择账单     │
│ 明细            │
│ (hds_invoice_   │
│  detail)        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 4. 关联账单     │
│ 信息            │
│ (hds_payment_   │
│  bill)          │
└─────────────────┘

数据流向：
门店信息 → 发票申请 → 账单明细 → 账单数据
```

## 发票状态流转

### 发票申请状态机

```
                    ┌─────────────┐
                    │   PENDING   │ ◄─── 待审核状态
                    │  (待审核)   │      (用户提交申请)
                    └──────┬──────┘
                           │
                           ▼
                    ┌─────────────┐
                    │ REVIEWING   │ ◄─── 审核中状态
                    │ (审核中)    │      (开始人工审核)
                    └──────┬──────┘
                           │
            ┌──────────────┼──────────────┐
            │              │              │
            ▼              ▼              ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │  APPROVED   │ │  REJECTED   │ │ INVOICING   │
    │  (已通过)   │ │  (已驳回)   │ │ (开票中)    │
    └──────┬──────┘ └──────┬──────┘ └──────┬──────┘
           │               │               │
           ▼               ▼               ▼
    ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
    │ 等待开票     │ │ 申请结束     │ │ 调用开票API │
    └──────┬──────┘ └─────────────┘ └──────┬──────┘
           │                               │
           └───────────────┬───────────────┘
                           │
                           ▼
                    ┌─────────────┐
                    │  INVOICED   │ ◄─── 已开票状态
                    │  (已开票)   │      (开票完成)
                    └─────────────┘

状态说明：
• PENDING   → REVIEWING : 开始审核
• REVIEWING → APPROVED  : 审核通过
• REVIEWING → REJECTED  : 审核驳回
• APPROVED  → INVOICING : 开始开票
• INVOICING → INVOICED  : 开票完成
```

## 数据示例

### 完整业务数据示例

```
1. 发票信息表 (hds_invoice_info)
┌─────────────────────────────────────────────────────────────────────────────┐
│ hotel_code: HOTEL_001                                                      │
│ invoice_title: 北京某某酒店有限公司                                          │
│ tax_number: 91110000000000000X                                             │
│ receive_email: <EMAIL>                                        │
│ invoice_content: 技术服务费                                                 │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
2. 发票申请表 (hds_invoice_application)
┌─────────────────────────────────────────────────────────────────────────────┐
│ application_no: INV_20240115_000001                                        │
│ hotel_code: HOTEL_001                                                      │
│ invoice_amount: 2400.00                                                    │
│ bill_count: 3                                                              │
│ application_status: PENDING                                                │
└─────────────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
3. 发票明细表 (hds_invoice_detail)
┌─────────────────────────────────────────────────────────────────────────────┐
│ 明细1: bill_no: BILL_20240101_000001, amount: 800.00                      │
│ 明细2: bill_no: BILL_20240201_000002, amount: 800.00                      │
│ 明细3: bill_no: BILL_20240301_000003, amount: 800.00                      │
│ 总计: 2400.00                                                              │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 查询示例

### 常用查询SQL

```sql
-- 1. 查询门店的发票申请列表
SELECT 
    ia.application_no,
    ia.invoice_amount,
    ia.bill_count,
    ia.application_status,
    ia.created_at,
    ii.invoice_title
FROM hds_invoice_application ia
LEFT JOIN hds_invoice_info ii ON ia.hotel_code = ii.hotel_code
WHERE ia.hotel_code = 'HOTEL_001'
ORDER BY ia.created_at DESC;

-- 2. 查询发票申请的详细明细
SELECT 
    ia.application_no,
    ia.invoice_amount,
    id.bill_no,
    id.package_name,
    id.service_period,
    id.bill_amount
FROM hds_invoice_application ia
LEFT JOIN hds_invoice_detail id ON ia.id = id.application_id
WHERE ia.application_no = 'INV_20240115_000001'
ORDER BY id.created_at;

-- 3. 查询可开票的账单
SELECT 
    pb.id,
    pb.bill_no,
    pb.payment_amount,
    pb.transaction_at,
    po.package_name
FROM hds_payment_bill pb
LEFT JOIN hds_payment_order po ON pb.order_id = po.id
WHERE pb.hotel_code = 'HOTEL_001'
  AND pb.invoice_status = 'NONE'
  AND pb.transaction_status = 'SUCCESS'
ORDER BY pb.transaction_at DESC;
```

## 索引优化策略

### 关键索引设计

```sql
-- 发票信息表索引
CREATE INDEX idx_invoice_info_hotel_code ON hds_invoice_info (hotel_code);

-- 发票申请表索引
CREATE INDEX idx_invoice_app_hotel_status ON hds_invoice_application (hotel_code, application_status);
CREATE INDEX idx_invoice_app_status_time ON hds_invoice_application (application_status, created_at);

-- 发票明细表索引
CREATE INDEX idx_invoice_detail_app_bill ON hds_invoice_detail (application_id, bill_id);

-- 复合索引优化查询性能
CREATE INDEX idx_invoice_app_complex ON hds_invoice_application 
(hotel_code, application_status, created_at, invoice_amount);
```

这个发票表设计完整支持发票申请、审核、开票的全流程管理，与支付账单系统无缝集成。
